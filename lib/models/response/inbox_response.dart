// To parse this JSON data, do
//
//     final inboxResponse = inboxResponseFromJson(jsonString);

import 'dart:convert';

InboxResponse inboxResponseFromJson(String str) =>
    InboxResponse.fromJson(json.decode(str));

String inboxResponseToJson(InboxResponse data) => json.encode(data.toJson());

class InboxResponse {
  List<InboxModel>? content;
  Pageable? pageable;
  bool? last;
  int? totalPages;
  int? totalElements;
  int? size;
  int? number;
  Sort? sort;
  bool? first;
  int? numberOfElements;
  bool? empty;

  InboxResponse({
    this.content,
    this.pageable,
    this.last,
    this.totalPages,
    this.totalElements,
    this.size,
    this.number,
    this.sort,
    this.first,
    this.numberOfElements,
    this.empty,
  });

  factory InboxResponse.fromJson(Map<String, dynamic> json) => InboxResponse(
    content:
        json["content"] == null
            ? []
            : List<InboxModel>.from(
              json["content"]!.map((x) => InboxModel.fromJson(x)),
            ),
    pageable:
        json["pageable"] == null ? null : Pageable.fromJson(json["pageable"]),
    last: json["last"],
    totalPages: json["totalPages"],
    totalElements: json["totalElements"],
    size: json["size"],
    number: json["number"],
    sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
    first: json["first"],
    numberOfElements: json["numberOfElements"],
    empty: json["empty"],
  );

  Map<String, dynamic> toJson() => {
    "content":
        content == null
            ? []
            : List<dynamic>.from(content!.map((x) => x.toJson())),
    "pageable": pageable?.toJson(),
    "last": last,
    "totalPages": totalPages,
    "totalElements": totalElements,
    "size": size,
    "number": number,
    "sort": sort?.toJson(),
    "first": first,
    "numberOfElements": numberOfElements,
    "empty": empty,
  };
}

class InboxModel {
  int? id;
  User? user;
  String? trxType;
  String? inboxType;
  int? trxId;
  String? title;
  String? body;
  bool? isArchived;
  bool? isRead;
  dynamic detailData;
  String? createdAt;
  String? updatedAt;
  bool inboxSelected;

  InboxModel({
    this.id,
    this.user,
    this.trxType,
    this.inboxType,
    this.trxId,
    this.title,
    this.body,
    this.isArchived,
    this.isRead,
    this.detailData,
    this.createdAt,
    this.updatedAt,
    this.inboxSelected = false,
  });

  factory InboxModel.fromJson(Map<String, dynamic> json) => InboxModel(
    id: json["id"],
    user: json["user"] == null ? null : User.fromJson(json["user"]),
    trxType: json["trxType"],
    inboxType: json["inboxType"],
    trxId: json["trxId"],
    title: json["title"],
    body: json["body"],
    isArchived: json["isArchived"],
    isRead: json["isRead"],
    detailData: json["detailData"],
    createdAt: json["createdAt"],
    updatedAt: json["updatedAt"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "user": user?.toJson(),
    "trxType": trxType,
    "inboxType": inboxType,
    "trxId": trxId,
    "title": title,
    "body": body,
    "isArchived": isArchived,
    "isRead": isRead,
    "detailData": detailData,
    "createdAt": createdAt,
    "updatedAt": updatedAt,
  };
}

class User {
  String? username;
  String? name;
  String? channel;
  String? agentCode;
  String? agentLevel;

  User({
    this.username,
    this.name,
    this.channel,
    this.agentCode,
    this.agentLevel,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
    username: json["username"],
    name: json["name"],
    channel: json["channel"],
    agentCode: json["agentCode"],
    agentLevel: json["agentLevel"],
  );

  Map<String, dynamic> toJson() => {
    "username": username,
    "name": name,
    "channel": channel,
    "agentCode": agentCode,
    "agentLevel": agentLevel,
  };
}

class Pageable {
  int? pageNumber;
  int? pageSize;
  Sort? sort;
  int? offset;
  bool? unpaged;
  bool? paged;

  Pageable({
    this.pageNumber,
    this.pageSize,
    this.sort,
    this.offset,
    this.unpaged,
    this.paged,
  });

  factory Pageable.fromJson(Map<String, dynamic> json) => Pageable(
    pageNumber: json["pageNumber"],
    pageSize: json["pageSize"],
    sort: json["sort"] == null ? null : Sort.fromJson(json["sort"]),
    offset: json["offset"],
    unpaged: json["unpaged"],
    paged: json["paged"],
  );

  Map<String, dynamic> toJson() => {
    "pageNumber": pageNumber,
    "pageSize": pageSize,
    "sort": sort?.toJson(),
    "offset": offset,
    "unpaged": unpaged,
    "paged": paged,
  };
}

class Sort {
  bool? empty;
  bool? sorted;
  bool? unsorted;

  Sort({this.empty, this.sorted, this.unsorted});

  factory Sort.fromJson(Map<String, dynamic> json) => Sort(
    empty: json["empty"],
    sorted: json["sorted"],
    unsorted: json["unsorted"],
  );

  Map<String, dynamic> toJson() => {
    "empty": empty,
    "sorted": sorted,
    "unsorted": unsorted,
  };
}
