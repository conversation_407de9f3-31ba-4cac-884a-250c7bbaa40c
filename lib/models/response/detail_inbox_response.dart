// To parse this JSON data, do
//
//     final detailInboxResponse = detailInboxResponseFromJson(jsonString);

import 'dart:convert';

DetailInboxResponse detailInboxResponseFromJson(String str) =>
    DetailInboxResponse.fromJson(json.decode(str));

String detailInboxResponseToJson(DetailInboxResponse data) =>
    json.encode(data.toJson());

class DetailInboxResponse {
  int? id;
  User? user;
  String? trxType;
  String? inboxType;
  int? trxId;
  String? title;
  String? body;
  String? footer;
  bool? isArchived;
  bool? isRead;
  DetailData? detailData;
  String? createdAt;
  String? updatedAt;

  DetailInboxResponse({
    this.id,
    this.user,
    this.trxType,
    this.inboxType,
    this.trxId,
    this.title,
    this.body,
    this.footer,
    this.isArchived,
    this.isRead,
    this.detailData,
    this.createdAt,
    this.updatedAt,
  });

  factory DetailInboxResponse.fromJson(Map<String, dynamic> json) =>
      DetailInboxResponse(
        id: json["id"],
        user: json["user"] == null ? null : User.fromJson(json["user"]),
        trxType: json["trxType"],
        inboxType: json["inboxType"],
        trxId: json["trxId"],
        title: json["title"],
        body: json["body"],
        footer: json["footer"],
        isArchived: json["isArchived"],
        isRead: json["isRead"],
        detailData:
            json["detailData"] == null
                ? null
                : DetailData.fromJson(json["detailData"]),
        createdAt: json["createdAt"],
        updatedAt: json["updatedAt"],
      );

  Map<String, dynamic> toJson() => {
    "id": id,
    "user": user?.toJson(),
    "trxType": trxType,
    "inboxType": inboxType,
    "trxId": trxId,
    "title": title,
    "body": body,
    "footer": footer,
    "isArchived": isArchived,
    "isRead": isRead,
    "detailData": detailData?.toJson(),
    "createdAt": createdAt,
    "updatedAt": updatedAt,
  };
}

class DetailData {
  int? id;
  Agent? agent;
  Agent? recruiter;
  ApprovalHeader? approvalHeader;
  String? data;
  String? approvalStatus;
  String? detailApproval;
  String? createdAt;
  String? updatedAt;
  String? positionLevel;

  DetailData({
    this.id,
    this.agent,
    this.recruiter,
    this.approvalHeader,
    this.data,
    this.approvalStatus,
    this.detailApproval,
    this.createdAt,
    this.updatedAt,
    this.positionLevel,
  });

  factory DetailData.fromJson(Map<String, dynamic> json) => DetailData(
    id: json["id"],
    agent: json["agent"] == null ? null : Agent.fromJson(json["agent"]),
    recruiter:
        json["recruiter"] == null ? null : Agent.fromJson(json["recruiter"]),
    approvalHeader:
        json["approvalHeader"] == null
            ? null
            : ApprovalHeader.fromJson(json["approvalHeader"]),
    data: json["data"],
    approvalStatus: json["approvalStatus"],
    detailApproval: json["detailApproval"],
    createdAt: json["createdAt"],
    updatedAt: json["updatedAt"],
    positionLevel: json["positionLevel"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "agent": agent?.toJson(),
    "recruiter": recruiter?.toJson(),
    "approvalHeader": approvalHeader?.toJson(),
    "data": data,
    "approvalStatus": approvalStatus,
    "detailApproval": detailApproval,
    "createdAt": createdAt,
    "updatedAt": updatedAt,
    "positionLevel": positionLevel,
  };
}

class Agent {
  int? id;
  String? leaderCode;
  String? leaderName;
  String? agentCode;
  String? agentName;
  String? distributionCode;
  String? roleName;
  String? level;
  String? positionLevel;
  String? regionCode;
  String? regionName;
  String? subRegionCode;
  String? subRegionName;
  String? areaCode;
  String? areaName;
  String? branchCode;
  String? branchName;
  dynamic groupCode;
  dynamic groupName;
  String? licenseNumberAaji;
  DateTime? licenseExpiredDateAaji;
  dynamic licenseNumberAasi;
  dynamic licenseExpiredDateAasi;
  dynamic leaderG2G;
  String? recruiterCode;
  DateTime? dob;
  String? gender;
  String? education;
  String? status;
  String? channel;
  String? email;
  dynamic phoneNumber;
  dynamic address;
  dynamic bankAccountNumber;
  dynamic bank;
  dynamic maritalStatus;
  dynamic bankAttachment;
  dynamic ktpAttachment;
  dynamic kkAttachment;
  dynamic photo;
  String? createdAt;
  String? updatedAt;
  List<Role>? roles;
  List<Branch>? branches;
  dynamic mbranchCode;
  dynamic mbranchName;
  dynamic sbranchCode;
  dynamic sbranchName;

  Agent({
    this.id,
    this.leaderCode,
    this.leaderName,
    this.agentCode,
    this.agentName,
    this.distributionCode,
    this.roleName,
    this.level,
    this.positionLevel,
    this.regionCode,
    this.regionName,
    this.subRegionCode,
    this.subRegionName,
    this.areaCode,
    this.areaName,
    this.branchCode,
    this.branchName,
    this.groupCode,
    this.groupName,
    this.licenseNumberAaji,
    this.licenseExpiredDateAaji,
    this.licenseNumberAasi,
    this.licenseExpiredDateAasi,
    this.leaderG2G,
    this.recruiterCode,
    this.dob,
    this.gender,
    this.education,
    this.status,
    this.channel,
    this.email,
    this.phoneNumber,
    this.address,
    this.bankAccountNumber,
    this.bank,
    this.maritalStatus,
    this.bankAttachment,
    this.ktpAttachment,
    this.kkAttachment,
    this.photo,
    this.createdAt,
    this.updatedAt,
    this.roles,
    this.branches,
    this.mbranchCode,
    this.mbranchName,
    this.sbranchCode,
    this.sbranchName,
  });

  factory Agent.fromJson(Map<String, dynamic> json) => Agent(
    id: json["id"],
    leaderCode: json["leaderCode"],
    leaderName: json["leaderName"],
    agentCode: json["agentCode"],
    agentName: json["agentName"],
    distributionCode: json["distributionCode"],
    roleName: json["roleName"],
    level: json["level"],
    positionLevel: json["positionLevel"],
    regionCode: json["regionCode"],
    regionName: json["regionName"],
    subRegionCode: json["subRegionCode"],
    subRegionName: json["subRegionName"],
    areaCode: json["areaCode"],
    areaName: json["areaName"],
    branchCode: json["branchCode"],
    branchName: json["branchName"],
    groupCode: json["groupCode"],
    groupName: json["groupName"],
    licenseNumberAaji: json["licenseNumberAAJI"],
    licenseExpiredDateAaji:
        json["licenseExpiredDateAAJI"] == null
            ? null
            : DateTime.parse(json["licenseExpiredDateAAJI"]),
    licenseNumberAasi: json["licenseNumberAASI"],
    licenseExpiredDateAasi: json["licenseExpiredDateAASI"],
    leaderG2G: json["leaderG2G"],
    recruiterCode: json["recruiterCode"],
    dob: json["dob"] == null ? null : DateTime.parse(json["dob"]),
    gender: json["gender"],
    education: json["education"],
    status: json["status"],
    channel: json["channel"],
    email: json["email"],
    phoneNumber: json["phoneNumber"],
    address: json["address"],
    bankAccountNumber: json["bankAccountNumber"],
    bank: json["bank"],
    maritalStatus: json["maritalStatus"],
    bankAttachment: json["bankAttachment"],
    ktpAttachment: json["ktpAttachment"],
    kkAttachment: json["kkAttachment"],
    photo: json["photo"],
    createdAt: json["createdAt"],
    updatedAt: json["updatedAt"],
    roles:
        json["roles"] == null
            ? []
            : List<Role>.from(json["roles"]!.map((x) => Role.fromJson(x))),
    branches:
        json["branches"] == null
            ? []
            : List<Branch>.from(
              json["branches"]!.map((x) => Branch.fromJson(x)),
            ),
    mbranchCode: json["mbranchCode"],
    mbranchName: json["mbranchName"],
    sbranchCode: json["sbranchCode"],
    sbranchName: json["sbranchName"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "leaderCode": leaderCode,
    "leaderName": leaderName,
    "agentCode": agentCode,
    "agentName": agentName,
    "distributionCode": distributionCode,
    "roleName": roleName,
    "level": level,
    "positionLevel": positionLevel,
    "regionCode": regionCode,
    "regionName": regionName,
    "subRegionCode": subRegionCode,
    "subRegionName": subRegionName,
    "areaCode": areaCode,
    "areaName": areaName,
    "branchCode": branchCode,
    "branchName": branchName,
    "groupCode": groupCode,
    "groupName": groupName,
    "licenseNumberAAJI": licenseNumberAaji,
    "licenseExpiredDateAAJI":
        "${licenseExpiredDateAaji!.year.toString().padLeft(4, '0')}-${licenseExpiredDateAaji!.month.toString().padLeft(2, '0')}-${licenseExpiredDateAaji!.day.toString().padLeft(2, '0')}",
    "licenseNumberAASI": licenseNumberAasi,
    "licenseExpiredDateAASI": licenseExpiredDateAasi,
    "leaderG2G": leaderG2G,
    "recruiterCode": recruiterCode,
    "dob":
        "${dob!.year.toString().padLeft(4, '0')}-${dob!.month.toString().padLeft(2, '0')}-${dob!.day.toString().padLeft(2, '0')}",
    "gender": gender,
    "education": education,
    "status": status,
    "channel": channel,
    "email": email,
    "phoneNumber": phoneNumber,
    "address": address,
    "bankAccountNumber": bankAccountNumber,
    "bank": bank,
    "maritalStatus": maritalStatus,
    "bankAttachment": bankAttachment,
    "ktpAttachment": ktpAttachment,
    "kkAttachment": kkAttachment,
    "photo": photo,
    "createdAt": createdAt,
    "updatedAt": updatedAt,
    "roles":
        roles == null ? [] : List<dynamic>.from(roles!.map((x) => x.toJson())),
    "branches":
        branches == null
            ? []
            : List<dynamic>.from(branches!.map((x) => x.toJson())),
    "mbranchCode": mbranchCode,
    "mbranchName": mbranchName,
    "sbranchCode": sbranchCode,
    "sbranchName": sbranchName,
  };
}

class Branch {
  int? id;
  String? branchCode;
  String? branchName;
  dynamic parentBranchCode;
  dynamic parentBranchName;
  String? phoneNumber;
  dynamic staffCount;
  String? city;
  String? address;
  String? secondAddress;
  String? thirdAddress;
  dynamic googleMapsUrl;
  dynamic latitude;
  dynamic longitude;
  bool? isActive;
  String? channel;
  String? createdAt;
  String? updatedAt;

  Branch({
    this.id,
    this.branchCode,
    this.branchName,
    this.parentBranchCode,
    this.parentBranchName,
    this.phoneNumber,
    this.staffCount,
    this.city,
    this.address,
    this.secondAddress,
    this.thirdAddress,
    this.googleMapsUrl,
    this.latitude,
    this.longitude,
    this.isActive,
    this.channel,
    this.createdAt,
    this.updatedAt,
  });

  factory Branch.fromJson(Map<String, dynamic> json) => Branch(
    id: json["id"],
    branchCode: json["branchCode"],
    branchName: json["branchName"],
    parentBranchCode: json["parentBranchCode"],
    parentBranchName: json["parentBranchName"],
    phoneNumber: json["phoneNumber"],
    staffCount: json["staffCount"],
    city: json["city"],
    address: json["address"],
    secondAddress: json["secondAddress"],
    thirdAddress: json["thirdAddress"],
    googleMapsUrl: json["googleMapsUrl"],
    latitude: json["latitude"],
    longitude: json["longitude"],
    isActive: json["isActive"],
    channel: json["channel"],
    createdAt: json["createdAt"],
    updatedAt: json["updatedAt"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "branchCode": branchCode,
    "branchName": branchName,
    "parentBranchCode": parentBranchCode,
    "parentBranchName": parentBranchName,
    "phoneNumber": phoneNumber,
    "staffCount": staffCount,
    "city": city,
    "address": address,
    "secondAddress": secondAddress,
    "thirdAddress": thirdAddress,
    "googleMapsUrl": googleMapsUrl,
    "latitude": latitude,
    "longitude": longitude,
    "isActive": isActive,
    "channel": channel,
    "createdAt": createdAt,
    "updatedAt": updatedAt,
  };
}

class Role {
  int? id;
  String? code;
  String? name;
  List<Access>? accesses;

  Role({this.id, this.code, this.name, this.accesses});

  factory Role.fromJson(Map<String, dynamic> json) => Role(
    id: json["id"],
    code: json["code"],
    name: json["name"],
    accesses:
        json["accesses"] == null
            ? []
            : List<Access>.from(
              json["accesses"]!.map((x) => Access.fromJson(x)),
            ),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "code": code,
    "name": name,
    "accesses":
        accesses == null
            ? []
            : List<dynamic>.from(accesses!.map((x) => x.toJson())),
  };
}

class Access {
  int? id;
  String? category;
  String? domain;
  String? action;

  Access({this.id, this.category, this.domain, this.action});

  factory Access.fromJson(Map<String, dynamic> json) => Access(
    id: json["id"],
    category: json["category"],
    domain: json["domain"],
    action: json["action"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "category": category,
    "domain": domain,
    "action": action,
  };
}

class ApprovalHeader {
  int? id;
  String? requestId;
  User? requestBy;
  String? trxType;
  int? trxId;
  String? approvalStatus;
  int? currentLevel;
  String? approverRole;
  int? maxLevel;
  dynamic remarks;
  List<ApprovalDetail>? approvalDetails;
  String? detailApproval;
  dynamic lastApproverRole;
  dynamic detailData;
  dynamic lastLevel;
  String? createdAt;
  String? updatedAt;

  ApprovalHeader({
    this.id,
    this.requestId,
    this.requestBy,
    this.trxType,
    this.trxId,
    this.approvalStatus,
    this.currentLevel,
    this.approverRole,
    this.maxLevel,
    this.remarks,
    this.approvalDetails,
    this.detailApproval,
    this.lastApproverRole,
    this.detailData,
    this.lastLevel,
    this.createdAt,
    this.updatedAt,
  });

  factory ApprovalHeader.fromJson(Map<String, dynamic> json) => ApprovalHeader(
    id: json["id"],
    requestId: json["requestId"],
    requestBy:
        json["requestBy"] == null ? null : User.fromJson(json["requestBy"]),
    trxType: json["trxType"],
    trxId: json["trxId"],
    approvalStatus: json["approvalStatus"],
    currentLevel: json["currentLevel"],
    approverRole: json["approverRole"],
    maxLevel: json["maxLevel"],
    remarks: json["remarks"],
    approvalDetails:
        json["approvalDetails"] == null
            ? []
            : List<ApprovalDetail>.from(
              json["approvalDetails"]!.map((x) => ApprovalDetail.fromJson(x)),
            ),
    detailApproval: json["detailApproval"],
    lastApproverRole: json["lastApproverRole"],
    detailData: json["detailData"],
    lastLevel: json["lastLevel"],
    createdAt: json["createdAt"],
    updatedAt: json["updatedAt"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "requestId": requestId,
    "requestBy": requestBy?.toJson(),
    "trxType": trxType,
    "trxId": trxId,
    "approvalStatus": approvalStatus,
    "currentLevel": currentLevel,
    "approverRole": approverRole,
    "maxLevel": maxLevel,
    "remarks": remarks,
    "approvalDetails":
        approvalDetails == null
            ? []
            : List<dynamic>.from(approvalDetails!.map((x) => x.toJson())),
    "detailApproval": detailApproval,
    "lastApproverRole": lastApproverRole,
    "detailData": detailData,
    "lastLevel": lastLevel,
    "createdAt": createdAt,
    "updatedAt": updatedAt,
  };
}

class ApprovalDetail {
  int? id;
  User? actionBy;
  String? remarks;
  String? approvalStatus;
  String? detailApproval;
  int? levelNumber;
  String? createdAt;

  ApprovalDetail({
    this.id,
    this.actionBy,
    this.remarks,
    this.approvalStatus,
    this.detailApproval,
    this.levelNumber,
    this.createdAt,
  });

  factory ApprovalDetail.fromJson(Map<String, dynamic> json) => ApprovalDetail(
    id: json["id"],
    actionBy: json["actionBy"] == null ? null : User.fromJson(json["actionBy"]),
    remarks: json["remarks"],
    approvalStatus: json["approvalStatus"],
    detailApproval: json["detailApproval"],
    levelNumber: json["levelNumber"],
    createdAt: json["createdAt"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "actionBy": actionBy?.toJson(),
    "remarks": remarks,
    "approvalStatus": approvalStatus,
    "detailApproval": detailApproval,
    "levelNumber": levelNumber,
    "createdAt": createdAt,
  };
}

class User {
  String? username;
  String? name;
  String? channel;
  String? agentCode;
  String? agentLevel;
  List<Branch>? branches;

  User({
    this.username,
    this.name,
    this.channel,
    this.agentCode,
    this.agentLevel,
    this.branches,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
    username: json["username"],
    name: json["name"],
    channel: json["channel"],
    agentCode: json["agentCode"],
    agentLevel: json["agentLevel"],
    branches:
        json["branches"] == null
            ? []
            : List<Branch>.from(
              json["branches"]!.map((x) => Branch.fromJson(x)),
            ),
  );

  Map<String, dynamic> toJson() => {
    "username": username,
    "name": name,
    "channel": channel,
    "agentCode": agentCode,
    "agentLevel": agentLevel,
    "branches":
        branches == null
            ? []
            : List<dynamic>.from(branches!.map((x) => x.toJson())),
  };
}
