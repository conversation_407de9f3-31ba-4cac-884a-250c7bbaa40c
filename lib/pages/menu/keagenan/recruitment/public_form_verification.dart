import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/public_form_verification_controller.dart';
import 'package:pdl_superapp/utils/constants.dart';

/// Public form verification component
class PublicFormVerification extends StatelessWidget {
  final PublicFormVerificationController controller;

  const PublicFormVerification({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Verifikasi Identitas',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: paddingMedium),
          Text(
            'Masukkan data diri Anda untuk verifikasi identitas',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  Get.isDarkMode ? kColorTextTersierLight : kColorTextTersier,
            ),
          ),
          SizedBox(height: paddingLarge),

          // NIK Field
          PdlTextField(
            textController: controller.nikController,
            label: 'NIK (Nomor Induk Kependudukan)',
            hint: 'Masukkan 16 digit NIK',
            keyboardType: TextInputType.number,
            maxLength: 16,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'NIK wajib diisi';
              }
              if (value.length != 16) {
                return 'NIK harus 16 digit';
              }
              if (!RegExp(r'^\d{16}$').hasMatch(value)) {
                return 'NIK hanya boleh berisi angka';
              }
              return null;
            },
          ),
          SizedBox(height: paddingMedium),

          // Phone Field
          PdlTextField(
            textController: controller.phoneController,
            label: 'Nomor Telepon',
            hint: 'Contoh: 08123456789',
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Nomor telepon wajib diisi';
              }
              if (value.length < 10) {
                return 'Nomor telepon minimal 10 digit';
              }
              if (!value.startsWith('08') && !value.startsWith('+62')) {
                return 'Nomor telepon harus dimulai dengan 08 atau +62';
              }
              return null;
            },
          ),
          SizedBox(height: paddingMedium),

          // Email Field
          PdlTextField(
            textController: controller.emailController,
            label: 'Email',
            hint: 'Contoh: <EMAIL>',
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Email wajib diisi';
              }
              if (!RegExp(
                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
              ).hasMatch(value)) {
                return 'Format email tidak valid';
              }
              return null;
            },
          ),
          SizedBox(height: paddingLarge),

          // Validation Status
          Obx(
            () => Column(
              children: [
                if (!controller.isFormValid.value &&
                    (controller.nikController.text.isNotEmpty ||
                        controller.phoneController.text.isNotEmpty ||
                        controller.emailController.text.isNotEmpty))
                  Container(
                    padding: EdgeInsets.all(paddingMedium),
                    decoration: BoxDecoration(
                      color: kColorGlobalBgWarning,
                      borderRadius: BorderRadius.circular(radiusMedium),
                      border: Border.all(
                        color: kColorGlobalWarning.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.warning_amber,
                          color: kColorGlobalWarning,
                          size: 20,
                        ),
                        SizedBox(width: paddingSmall),
                        Expanded(
                          child: Text(
                            'Mohon lengkapi semua field dengan benar',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: kColorGlobalWarning),
                          ),
                        ),
                      ],
                    ),
                  ),

                if (controller.isFormValid.value)
                  Container(
                    padding: EdgeInsets.all(paddingMedium),
                    decoration: BoxDecoration(
                      color: kColorGlobalBgGreen,
                      borderRadius: BorderRadius.circular(radiusMedium),
                      border: Border.all(
                        color: kColorGlobalGreen.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: kColorGlobalGreen,
                          size: 20,
                        ),
                        SizedBox(width: paddingSmall),
                        Expanded(
                          child: Text(
                            'Data verifikasi sudah lengkap',
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: kColorGlobalGreen),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),

          SizedBox(height: paddingLarge),

          // Info Text
          Container(
            padding: EdgeInsets.all(paddingMedium),
            decoration: BoxDecoration(
              color: kColorGlobalBgBlue,
              borderRadius: BorderRadius.circular(radiusMedium),
              border: Border.all(
                color: kColorGlobalBlue.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info_outline, color: kColorGlobalBlue, size: 20),
                    SizedBox(width: paddingSmall),
                    Text(
                      'Informasi Penting',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: kColorGlobalBlue,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: paddingSmall),
                Text(
                  '• Pastikan data yang dimasukkan sesuai dengan dokumen resmi\n'
                  '• NIK akan digunakan untuk verifikasi identitas\n'
                  '• Email akan digunakan untuk komunikasi selanjutnya\n'
                  '• Nomor telepon harus aktif dan dapat dihubungi',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: kColorGlobalBlue),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
