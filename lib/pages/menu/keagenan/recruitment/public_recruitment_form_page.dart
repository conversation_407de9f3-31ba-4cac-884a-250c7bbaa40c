import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/public_recruitment_form_controller.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/form_identification.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/form_self_identification.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/form_terms_signature.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/form_verification.dart';
import 'package:pdl_superapp/pages/widget/public_page_wrapper.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

/// Public version of recruitment form page
/// Accessible without authentication token
class PublicRecruitmentFormPage extends StatelessWidget {
  PublicRecruitmentFormPage({super.key});

  final PublicRecruitmentFormController controller = Get.put(
    PublicRecruitmentFormController(),
  );

  @override
  Widget build(BuildContext context) {
    return PublicPageWrapper(
      showAppBar: true,
      title: 'Formulir Rekrutmen',
      onBack: () => Get.back(),
      child: Obx(
        () => Stack(
          children: [
            _formPages(context),
            if (controller.isVerificationEmailSent.isTrue)
              _verificationPages(context),
          ],
        ),
      ),
    );
  }

  Widget _verificationPages(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Verifikasi Email Kandidat'),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () {
            controller.isVerificationEmailSent.value = false;
          },
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        foregroundColor: Theme.of(context).textTheme.bodyLarge?.color,
      ),
      body: controller.selfIdentificationController.isEmailVerified.value
          ? _emailVerifiedPage(context)
          : _verifSentPage(context),
    );
  }

  Widget _emailVerifiedPage(BuildContext context) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Utils.cachedSvgWrapper('icon/ic-dialog-check.svg'),
          SizedBox(height: paddingLarge),
          Text(
            'Email Berhasil Diverifikasi',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: paddingMedium),
          Text(
            'Mohon menunggu Anda akan secara otomatis diarahkan ke halaman berikutnya',
            textAlign: TextAlign.center,
          ),
          SizedBox(height: paddingMedium),
          CircularProgressIndicator(
            color: Get.isDarkMode ? kColorBorderLight : kColorBorderDark,
          ),
          SizedBox(height: paddingExtraLarge),
          RichText(
            text: TextSpan(
              style: Theme.of(context).textTheme.bodyMedium,
              children: [
                TextSpan(text: 'Atau '),
                WidgetSpan(
                  alignment: PlaceholderAlignment.middle,
                  child: GestureDetector(
                    onTap: () {
                      controller.activePage.value = 3;
                      controller.pageController.animateToPage(
                        3,
                        duration: Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                      controller.isVerificationEmailSent.value = false;
                    },
                    child: Text(
                      'klik disini',
                      style: Theme.of(context).textTheme.bodyMedium
                          ?.copyWith(color: kColorGlobalBlue),
                    ),
                  ),
                ),
                TextSpan(text: ' untuk melanjutkan secara manual'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _verifSentPage(BuildContext context) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Utils.cachedSvgWrapper(
            'icon/illustration-empty-inbox.svg',
            width: 200,
          ),
          SizedBox(height: paddingMedium),
          Text(
            'Segera Verifikasi Email Kandidat',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: paddingMedium),
          Text('Tautan verifikasi telah dikirimkan ke'),
          Obx(
            () => Text(
              controller.selfIdentificationController.emailVerif.value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
          SizedBox(height: paddingMedium),
          Text(
            'Belum mendapatkan tautan verifikasi?',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Get.isDarkMode ? kColorTextTersierLight : kColorTextTersier,
            ),
          ),
          SizedBox(height: paddingSmall),
          Obx(() {
            if (controller.selfIdentificationController.isCountdownActive.value) {
              return Text(
                'Kirim ulang dalam ${controller.selfIdentificationController.countdownDisplay.value}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Get.isDarkMode 
                      ? kColorTextTersierLight 
                      : kColorTextTersier,
                ),
              );
            } else {
              return GestureDetector(
                onTap: () {
                  controller.selfIdentificationController.restartCountdown();
                },
                child: Text(
                  'Kirim Ulang',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: kColorGlobalBlue,
                  ),
                ),
              );
            }
          }),
        ],
      ),
    );
  }

  Widget _formPages(BuildContext context) {
    return Scaffold(
      body: Container(
        padding: EdgeInsets.all(paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _pageCounter(context),
            SizedBox(height: paddingMedium),
            Expanded(
              child: Obx(
                () => controller.isReady.isFalse
                    ? Center(child: CircularProgressIndicator())
                    : ExpandablePageView(
                        controller: controller.pageController,
                        physics: NeverScrollableScrollPhysics(),
                        children: [
                          FormVerification(
                            controller: controller.verificationController,
                          ),
                          FormIdentification(
                            controller: controller.identificationController,
                          ),
                          FormSelfIdentification(
                            controller: controller.selfIdentificationController,
                          ),
                          FormTermsSignature(controller: controller),
                        ],
                      ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.all(paddingMedium),
        child: Obx(
          () => Row(
            children: [
              Expanded(
                child: PdlButton(
                  backgroundColor: Colors.transparent,
                  foregorundColor: kColorGlobalBlue,
                  title: 'button_save'.tr,
                  onPressed: () async {
                    final result = await controller.saveFormData();
                    // Result handling is done inside saveFormData method
                  },
                ),
              ),
              SizedBox(width: paddingSmall),
              Expanded(
                child: Obx(
                  () => PdlButton(
                    title: controller.activePage.value == 3
                        ? 'button_submit'.tr
                        : 'button_next'.tr,
                    onPressed: _getNextButtonAction(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  VoidCallback? _getNextButtonAction() {
    if (controller.activePage.value < 2) {
      return () {
        if (controller.validateCurrentPage()) {
          controller.pageController.nextPage(
            duration: Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
          controller.activePage.value += 1;
        } else {
          Utils.popup(
            body: 'Mohon lengkapi semua field yang wajib diisi',
            type: 'failed',
          );
        }
      };
    } else if (controller.activePage.value == 2) {
      return () {
        if (controller.validateCurrentPage()) {
          controller.submitFormForVerification();
        } else {
          Utils.popup(
            body: 'Mohon lengkapi semua field yang wajib diisi',
            type: 'failed',
          );
        }
      };
    } else {
      return () async {
        if (controller.validateCurrentPage()) {
          await controller.submitFormFinal();
        } else {
          Utils.popup(
            body: 'Mohon lengkapi semua field yang wajib diisi',
            type: 'failed',
          );
        }
      };
    }
  }

  Widget _pageCounter(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Obx(
        () => Row(
          children: [
            for (int i = 0; i < 4; i++)
              controller.activePage.value == i
                  ? Expanded(child: _pageCounterItem(context, i))
                  : _pageCounterItem(context, i),
          ],
        ),
      ),
    );
  }

  Widget _pageCounterItem(BuildContext context, int index) {
    return Obx(() {
      String title = 'Verifikasi Identitas';
      switch (index) {
        case 1:
          title = 'Data Diri Sesuai KTP';
          break;
        case 2:
          title = 'Kelengkapan Data';
          break;
        case 3:
          title = 'Perjanjian Keagenan';
          break;
        default:
      }
      return Row(
        children: [
          if (index < controller.activePage.value)
            GestureDetector(
              onTap: () {
                controller.activePage.value = index;
                controller.pageController.animateToPage(
                  index,
                  duration: Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                );
              },
              child: Icon(Icons.check_circle, color: kColorGlobalGreen),
            ),
          if (index >= controller.activePage.value)
            Container(
              padding: EdgeInsets.all(paddingSmall),
              decoration: BoxDecoration(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                shape: BoxShape.circle,
              ),
              child: Text(
                '${index + 1}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          SizedBox(width: paddingSmall),
          if (controller.activePage.value == index)
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w700,
              ),
            ),
          if (controller.activePage.value == index)
            SizedBox(width: paddingMedium),
          if (controller.activePage.value == index) 
            Expanded(child: Divider()),
          if (controller.activePage.value == index)
            SizedBox(width: paddingMedium),
        ],
      );
    });
  }
}
