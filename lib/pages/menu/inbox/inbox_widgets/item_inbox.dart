import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/models/response/inbox_response.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class ItemInbox extends StatelessWidget {
  const ItemInbox({
    super.key,
    required this.inbox,
    required this.onArchive,
    required this.onDelete,
    required this.onSelect,
    required this.icon,
    required this.onTap,
  });
  final InboxModel inbox;
  final String icon;
  final VoidCallback onArchive, onDelete, onSelect, onTap;

  @override
  Widget build(BuildContext context) {
    final createAtDateTime = DateTime.parse(inbox.createdAt ?? '');
    final currentDate = DateTime.now();

    final bool isSameDay = currentDate.difference(createAtDateTime).inDays == 0;

    return Container(
      margin: EdgeInsets.only(bottom: paddingSmall),
      child: Dismissible(
        key: Key(inbox.id.toString()),
        direction: DismissDirection.horizontal,
        background: Container(
          decoration: BoxDecoration(
            color: Color.fromRGBO(12, 157, 235, 1),
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(radiusMedium),
              bottomLeft: Radius.circular(radiusMedium),
            ),
          ),
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: Utils.cachedSvgWrapper(
            'icon/ic-linear-notes.svg',
            color: Colors.white,
          ),
        ),
        secondaryBackground: Container(
          decoration: BoxDecoration(
            color: Color.fromRGBO(175, 5, 25, 1),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(radiusMedium),
              bottomRight: Radius.circular(radiusMedium),
            ),
          ),
          alignment: Alignment.centerRight,
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: Utils.cachedSvgWrapper(
            'icon/ic-linear-trash-bin.svg',
            color: Colors.white,
          ),
        ),
        onDismissed: (direction) {
          if (direction == DismissDirection.startToEnd) {
            onArchive();
          } else {
            onDelete();
          }
        },
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: inbox.inboxSelected ? kLine : Colors.transparent,
            borderRadius: BorderRadius.circular(15),
          ),
          child: ListTile(
            contentPadding: EdgeInsets.zero,
            onTap: onTap,
            leading: InkWell(
              onTap: onSelect,
              child:
                  inbox.inboxSelected
                      ? Container(
                        decoration: BoxDecoration(
                          color: Colors.grey,
                          shape: BoxShape.circle,
                        ),
                        child: Utils.cachedSvgWrapper(
                          width: Get.width / 10,
                          'icon/ic-linear-tick.svg',
                          color: Colors.white,
                        ),
                      )
                      : Utils.cachedSvgWrapper(width: Get.width / 10, icon),
            ),
            title: Container(
              margin: EdgeInsets.only(bottom: paddingExtraSmall),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      inbox.title ?? '-',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color:
                            (inbox.isRead ?? false)
                                ? Color(0xFF888888)
                                : kColorTextLight,
                      ),
                    ),
                  ),
                  Text(
                    DateFormat(
                      isSameDay ? 'HH:mm' : 'dd MMM yyyy HH:mm',
                    ).format(createAtDateTime),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color:
                          (inbox.isRead ?? false)
                              ? Color(0xFF888888)
                              : kColorTextLight,
                    ),
                  ),
                ],
              ),
            ),

            subtitle: Text(
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              inbox.body ?? '-',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color:
                    (inbox.isRead ?? false)
                        ? Color(0xFF888888)
                        : kColorTextTersierLight,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
