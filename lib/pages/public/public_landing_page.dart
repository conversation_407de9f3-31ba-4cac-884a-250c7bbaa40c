import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/pages/widget/public_page_wrapper.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';

/// Halaman landing public untuk mendemonstrasikan akses tanpa token
class PublicLandingPage extends StatelessWidget {
  const PublicLandingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PublicPageWrapper(
      showAppBar: true,
      title: 'PDL SuperApp - Public',
      child: Scaffold(
        body: Container(
          padding: EdgeInsets.all(paddingMedium),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(Icons.public, size: 100, color: kColorGlobalBlue),
              SizedBox(height: paddingLarge),
              Text(
                'Selamat Datang di PDL SuperApp',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: kColorGlobalBlue,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: paddingMedium),
              Text(
                'Halaman ini dapat diakses tanpa perlu login terlebih dahulu.',
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: paddingExtraLarge),

              // Public Recruitment Form Button
              SizedBox(
                width: double.infinity,
                child: PdlButton(
                  title: 'Formulir Rekrutmen',
                  onPressed: () {
                    Get.toNamed(Routes.KEAGENAN_FORM_PUBLIC);
                  },
                  backgroundColor: kColorGlobalBlue,
                ),
              ),

              SizedBox(height: paddingMedium),

              // Login Button
              SizedBox(
                width: double.infinity,
                child: PdlButton(
                  title: 'Login ke Aplikasi',
                  onPressed: () {
                    Get.toNamed(Routes.LOGIN);
                  },
                  backgroundColor: Colors.transparent,
                  foregorundColor: kColorGlobalBlue,
                ),
              ),

              SizedBox(height: paddingMedium),

              // Test Access Button
              SizedBox(
                width: double.infinity,
                child: PdlButton(
                  title: 'Test Access Control',
                  onPressed: () {
                    Get.toNamed(Routes.TEST_ACCESS);
                  },
                  backgroundColor: Colors.transparent,
                  foregorundColor: kColorGlobalWarning,
                ),
              ),

              SizedBox(height: paddingLarge),

              // Info Text
              Container(
                padding: EdgeInsets.all(paddingMedium),
                decoration: BoxDecoration(
                  color: kColorGlobalBgBlue,
                  borderRadius: BorderRadius.circular(radiusMedium),
                  border: Border.all(
                    color: kColorGlobalBlue.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Icon(Icons.info_outline, color: kColorGlobalBlue, size: 24),
                    SizedBox(height: paddingSmall),
                    Text(
                      'Fitur Public',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: kColorGlobalBlue,
                      ),
                    ),
                    SizedBox(height: paddingSmall),
                    Text(
                      'Halaman ini menggunakan PublicPageWrapper dan PublicMiddleware, sehingga dapat diakses tanpa authentication token.',
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
