import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/pages/widget/public_page_wrapper.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Halaman test untuk mendemonstrasikan akses public vs private
class TestAccessPage extends StatelessWidget {
  const TestAccessPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PublicPageWrapper(
      showAppBar: true,
      title: 'Test Access Control',
      child: Scaffold(
        body: Container(
          padding: EdgeInsets.all(paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatusCard(),
              SizedBox(height: paddingLarge),
              _buildTestButtons(),
              SizedBox(height: paddingLarge),
              _buildInfoSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return FutureBuilder<bool>(
      future: _checkLoginStatus(),
      builder: (context, snapshot) {
        final isLoggedIn = snapshot.data ?? false;
        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(paddingMedium),
          decoration: BoxDecoration(
            color: isLoggedIn ? kColorGlobalBgGreen : kColorGlobalBgWarning,
            borderRadius: BorderRadius.circular(radiusMedium),
            border: Border.all(
              color:
                  isLoggedIn
                      ? kColorGlobalGreen.withValues(alpha: 0.3)
                      : kColorGlobalWarning.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            children: [
              Icon(
                isLoggedIn ? Icons.verified_user : Icons.person_off,
                color: isLoggedIn ? kColorGlobalGreen : kColorGlobalWarning,
                size: 32,
              ),
              SizedBox(height: paddingSmall),
              Text(
                isLoggedIn ? 'User Logged In' : 'User Not Logged In',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isLoggedIn ? kColorGlobalGreen : kColorGlobalWarning,
                ),
              ),
              SizedBox(height: paddingSmall),
              Text(
                isLoggedIn
                    ? 'Anda dapat mengakses halaman private dan public'
                    : 'Anda hanya dapat mengakses halaman public',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTestButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Test Navigation',
          style: Theme.of(
            Get.context!,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        SizedBox(height: paddingMedium),

        // Public Pages
        Text(
          'Halaman Public (Selalu Accessible)',
          style: Theme.of(Get.context!).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: kColorGlobalGreen,
          ),
        ),
        SizedBox(height: paddingSmall),

        SizedBox(
          width: double.infinity,
          child: PdlButton(
            title: 'Public Landing Page',
            onPressed: () => Get.toNamed(Routes.PUBLIC_LANDING),
            backgroundColor: kColorGlobalGreen,
          ),
        ),
        SizedBox(height: paddingSmall),

        SizedBox(
          width: double.infinity,
          child: PdlButton(
            title: 'Public Recruitment Form',
            onPressed: () => Get.toNamed(Routes.KEAGENAN_FORM_PUBLIC),
            backgroundColor: kColorGlobalGreen,
          ),
        ),

        SizedBox(height: paddingMedium),

        // Private Pages
        Text(
          'Halaman Private (Perlu Login)',
          style: Theme.of(Get.context!).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: kColorGlobalBlue,
          ),
        ),
        SizedBox(height: paddingSmall),

        SizedBox(
          width: double.infinity,
          child: PdlButton(
            title: 'Main Dashboard (Private)',
            onPressed: () => Get.toNamed(Routes.MAIN),
            backgroundColor: kColorGlobalBlue,
          ),
        ),
        SizedBox(height: paddingSmall),

        SizedBox(
          width: double.infinity,
          child: PdlButton(
            title: 'Private Recruitment Form',
            onPressed: () => Get.toNamed(Routes.KEAGENAN_FORM),
            backgroundColor: kColorGlobalBlue,
          ),
        ),

        SizedBox(height: paddingMedium),

        // Auth Pages
        Text(
          'Authentication',
          style: Theme.of(Get.context!).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: kColorGlobalWarning,
          ),
        ),
        SizedBox(height: paddingSmall),

        SizedBox(
          width: double.infinity,
          child: PdlButton(
            title: 'Login Page',
            onPressed: () => Get.toNamed(Routes.LOGIN),
            backgroundColor: kColorGlobalWarning,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoSection() {
    return Container(
      padding: EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        color: kColorGlobalBgBlue,
        borderRadius: BorderRadius.circular(radiusMedium),
        border: Border.all(color: kColorGlobalBlue.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: kColorGlobalBlue, size: 20),
              SizedBox(width: paddingSmall),
              Text(
                'Cara Testing',
                style: Theme.of(Get.context!).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: kColorGlobalBlue,
                ),
              ),
            ],
          ),
          SizedBox(height: paddingSmall),
          Text(
            '1. Tanpa Login: Coba akses halaman private → akan redirect ke login\n'
            '2. Dengan Login: Semua halaman dapat diakses\n'
            '3. Sudah Login + akses login page → akan redirect ke main\n'
            '4. Halaman public selalu dapat diakses',
            style: Theme.of(Get.context!).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Future<bool> _checkLoginStatus() async {
    try {
      final prefs = Get.find<SharedPreferences>();
      final token = prefs.getString(kStorageToken);
      return token != null && token.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
