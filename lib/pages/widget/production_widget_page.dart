import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:pdl_superapp/base/base_detail_page.dart';
import 'package:pdl_superapp/components/pdl_text_field.dart';
import 'package:pdl_superapp/components/title_widget.dart';
import 'package:pdl_superapp/components/widget/graphic_widget.dart';
import 'package:pdl_superapp/controllers/widget/production/production_widget_controller.dart';
import 'package:pdl_superapp/models/widget/widget_production_models.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/extensions/string_ext.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

/// A page that displays production data for different user levels.
///
/// This page shows production data in both monthly and yearly views,
/// with different tabs for individual and team/group data depending on user level.
class ProductionWidgetPage extends StatelessWidget {
  ProductionWidgetPage({super.key});

  final ProductionWidgetController controller = Get.put(
    ProductionWidgetController(),
  );

  @override
  Widget build(BuildContext context) {
    _initializePageArguments();

    return BaseDetailPage(
      title: 'Produksi Saya',
      controller: controller,
      onRefresh: () async => controller.onInit(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: paddingMedium),
        child: Obx(
          () => Column(
            children: [
              SizedBox(height: paddingLarge),
              _header(context),
              SizedBox(height: paddingMedium),
              _buildUserLevelSwitcher(context),
              SizedBox(height: paddingMedium),
              GraphicWidget(),
              SizedBox(height: paddingMedium),
              _buildDataContainer(context),
              SizedBox(height: paddingExtraLarge),
            ],
          ),
        ),
      ),
    );
  }

  /// Initializes page arguments and sets controller values accordingly
  void _initializePageArguments() {
    // Get and set time type from arguments
    final timeType = Get.arguments[kArgsProductionType] ?? '';

    if (timeType.isNotEmpty) {
      controller.selectedMonth.value = timeType;
    }

    // Note: We don't need to set the team type here anymore
    // It's already handled in the controller's onInit method
    // This prevents overriding the value after data has been loaded
  }

  /// Returns the appropriate data container based on selected time period
  Widget _buildDataContainer(BuildContext context) {
    return controller.selectedMonth.value == kSwitchMonthly
        ? _monthly(context)
        : _yearly(context);
  }

  /// Builds the user level switcher based on user level
  Widget _buildUserLevelSwitcher(BuildContext context) {
    final userLevel = Get.arguments[kArgsUserLevel] ?? '';
    return _switchContainer(context, level: userLevel);
  }

  /// Builds the monthly data container
  Container _monthly(BuildContext context) {
    return _buildDataContainerWithHeader(
      context,
      isMonthly: true,
      gradientColors: [Color(0xFF0083B0), Color(0xFF00B4DB)],
      periodText:
          'Bulan ${DateFormat('MMMM').format(DateTime.now())} ${DateTime.now().year}',
    );
  }

  /// Builds the yearly data container
  Container _yearly(BuildContext context) {
    return _buildDataContainerWithHeader(
      context,
      isMonthly: false,
      gradientColors: [Color(0xFF679047), Color(0xFF6FC36E)],
      periodText: 'Tahun ${DateTime.now().year}',
    );
  }

  /// Common method to build data container with header
  /// Reduces code duplication between monthly and yearly views
  Container _buildDataContainerWithHeader(
    BuildContext context, {
    required bool isMonthly,
    required List<Color> gradientColors,
    required String periodText,
  }) {
    return Container(
      width: Get.width,
      decoration: BoxDecoration(
        border: Border.all(
          color:
              controller.selectedType.value != kSwitchProdArea
                  ? (Get.isDarkMode ? kColorBorderDark : kColorBorderLight)
                  : Colors.transparent,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Obx(() {
        // Determine which data to display based on selected type and period
        final isIndividuSelected =
            controller.selectedType.value == kSwitchProdIndividu;
        final dataToShow = _getDataToShow(isMonthly, isIndividuSelected);

        // Get title prefix based on user level and selected type
        final titlePrefix = _getTitlePrefix(isIndividuSelected);

        return Column(
          children: [
            _buildHeaderContainer(
              context,
              titlePrefix: titlePrefix,
              periodText: periodText,
              gradientColors: gradientColors,
            ),
            if (controller.selectedType.value != kSwitchProdArea)
              _buildSearchField(context),
            _buildDataList(context, dataToShow, isIndividuSelected),
          ],
        );
      }),
    );
  }

  /// Returns the appropriate data list based on period and type selection
  List<WidgetProductionDetailModels> _getDataToShow(
    bool isMonthly,
    bool isIndividuSelected,
  ) {
    if (isMonthly) {
      return isIndividuSelected
          ? controller.filteredDataMonthly
          : controller.filteredDataTeamMonthly;
    } else {
      return isIndividuSelected
          ? controller.filteredDataYearly
          : controller.filteredDataTeamYearly;
    }
  }

  /// Returns the appropriate title prefix based on user level and selected type
  String _getTitlePrefix(bool isIndividuSelected) {
    final userLevel = Get.arguments[kArgsUserLevel] ?? '';

    if (isIndividuSelected) {
      return 'Polis Individu';
    }

    // For standard user levels
    if (userLevel == kUserLevelBd) {
      return 'Polis Group';
    } else if (userLevel == kUserLevelBm) {
      return 'Polis Team';
    }

    // For other user levels
    switch (controller.selectedType.value) {
      case kSwitchProdGroup:
        return 'Polis Group';
      case kSwitchProdBranch:
        return 'Polis Cabang';
      case kSwitchProdArea:
        return 'Polis Area';
      default:
        return 'Polis Team';
    }
  }

  /// Builds the header container with gradient background
  Container _buildHeaderContainer(
    BuildContext context, {
    required String titlePrefix,
    required String periodText,
    required List<Color> gradientColors,
  }) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        borderRadius:
            controller.selectedType.value == kSwitchProdArea
                ? BorderRadius.circular(16)
                : BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$titlePrefix $periodText',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: kColorBorderLight,
              fontWeight: FontWeight.w700,
            ),
          ),
          _buildNetApeText(context),
        ],
      ),
    );
  }

  /// Builds the NET APE text with reactive total
  Widget _buildNetApeText(BuildContext context) {
    return Obx(() {
      // Calculate the appropriate NET APE total based on the selected tab and user role
      num netApeTotal;

      if (controller.selectedType.value == kSwitchProdArea) {
        // For Area tab, use role-specific data
        final userRole = Get.arguments[kArgsUserLevel] ?? '';

        switch (userRole) {
          case kUserLevelABDD:
            netApeTotal = controller.calculateTotalNetApe(
              controller.getAbddData(),
            );
            break;
          case kUserLevelBdm:
            netApeTotal = controller.calculateTotalNetApe(
              controller.getBdmData(),
            );
            break;
          case kUserLevelBDD:
            netApeTotal = controller.calculateTotalNetApe(
              controller.getBddData(),
            );
            break;
          case kUserLevelHOS:
            netApeTotal = controller.calculateTotalNetApe(
              controller.getHosData(),
            );
            break;
          default:
            // Default to standard calculation for other roles
            netApeTotal = controller.getCurrentTotalNetApe();
        }
      } else {
        // For other tabs, use the standard calculation
        netApeTotal = controller.getCurrentTotalNetApe();
      }

      return RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: 'NET APE ',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: kColorBorderLight),
            ),
            TextSpan(
              text:
                  'Rp${Utils.currencyFormatters(data: netApeTotal.toString(), currency: '')}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: kColorBorderLight,
              ),
            ),
          ],
        ),
      );
    });
  }

  /// Builds the search field
  Widget _buildSearchField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(paddingMedium),
      child: PdlTextField(
        hint: 'Cari nama, kode agen atau nomor polis',
        onChanged: (value) => controller.onSearchTextChanged(value),
        prefixIcon: Padding(
          padding: EdgeInsets.all(paddingMedium),
          child: Utils.cachedSvgWrapper(
            'icon/ic-linear-search -2.svg',
            color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
          ),
        ),
      ),
    );
  }

  /// Builds the data list with appropriate card type
  Widget _buildDataList(
    BuildContext context,
    List<WidgetProductionDetailModels> dataToShow,
    bool isIndividuSelected,
  ) {
    // If Area tab is selected, show empty container
    if (controller.selectedType.value == kSwitchProdArea) {
      return _buildAreaTabContent(context);
    }

    return Column(
      children: [
        if (controller.state.value == ControllerState.loading)
          Padding(
            padding: EdgeInsets.only(bottom: paddingMedium),
            child: CircularProgressIndicator(),
          ),
        if (controller.state.value != ControllerState.loading &&
            dataToShow.isEmpty)
          Padding(
            padding: EdgeInsets.only(bottom: paddingMedium),
            child: Utils.cachedSvgWrapper(
              'icon/illustration-empty-doc-check.svg',
              width: Get.width / 2,
            ),
          ),
        for (int i = 0; i < dataToShow.length; i++)
          isIndividuSelected
              ? _contentCard(
                context,
                data: dataToShow[i],
                index: i,
                totalItems: dataToShow.length,
              )
              : _teamContentCard(
                context,
                data: dataToShow[i],
                index: i,
                totalItems: dataToShow.length,
              ),
      ],
    );
  }

  /// Builds a DataTable for the Area tab
  Widget _buildAreaTabContent(BuildContext context) {
    // Get the current user role
    final userRole = Get.arguments[kArgsUserLevel] ?? '';

    return Padding(
      padding: EdgeInsets.only(top: paddingMedium),
      child: Column(
        children: [
          if (controller.state.value == ControllerState.loading)
            Padding(
              padding: EdgeInsets.only(bottom: paddingMedium),
              child: CircularProgressIndicator(),
            ),
          if (controller.state.value != ControllerState.loading) ...[
            // Display tables based on user role according to the specified rules

            // BDM Section - shown for all roles
            _buildRoleSectionHeader(context, 'Produksi BDM', kUserLevelBdm),
            _buildNetApeSummary(
              context,
              controller.getBdmData(),
              roleKey: kUserLevelBdm,
            ),
            SizedBox(height: paddingMedium),
            controller.getBdmData().isEmpty
                ? _buildEmptyDataMessage(context, 'BDM')
                : _buildBdmTable(context, controller.getBdmData()),
            SizedBox(height: paddingLarge),

            // ABDD Section - shown for ABDD, BDD, HOS, and CAO roles
            if (userRole == kUserLevelABDD ||
                userRole == kUserLevelBDD ||
                userRole == kUserLevelHOS ||
                userRole == kUserLevelCAO) ...[
              _buildRoleSectionHeader(context, 'Produksi ABDD', kUserLevelABDD),
              _buildNetApeSummary(
                context,
                controller.getAbddData(),
                roleKey: kUserLevelABDD,
              ),
              SizedBox(height: paddingMedium),
              controller.getAbddData().isEmpty
                  ? _buildEmptyDataMessage(context, 'ABDD')
                  : _buildAbddTable(context, controller.getAbddData()),
              SizedBox(height: paddingLarge),
            ],

            // BDD Section - shown for BDD, HOS, and CAO roles
            if (userRole == kUserLevelBDD ||
                userRole == kUserLevelHOS ||
                userRole == kUserLevelCAO) ...[
              _buildRoleSectionHeader(context, 'Produksi BDD', kUserLevelBDD),
              _buildNetApeSummary(
                context,
                controller.getBddData(),
                roleKey: kUserLevelBDD,
              ),
              SizedBox(height: paddingMedium),
              controller.getBddData().isEmpty
                  ? _buildEmptyDataMessage(context, 'BDD')
                  : _buildBddTable(context, controller.getBddData()),
              SizedBox(height: paddingLarge),
            ],

            // HOS Section - shown for HOS and CAO roles
            if (userRole == kUserLevelHOS || userRole == kUserLevelCAO) ...[
              _buildRoleSectionHeader(context, 'Produksi HOS', kUserLevelHOS),
              _buildNetApeSummary(
                context,
                controller.getHosData(),
                roleKey: kUserLevelHOS,
              ),
              SizedBox(height: paddingMedium),
              controller.getHosData().isEmpty
                  ? _buildEmptyDataMessage(context, 'HOS')
                  : _buildHosTable(context, controller.getHosData()),
            ],
          ],
        ],
      ),
    );
  }

  /// Builds a section header with highlighting for the current user's role
  Widget _buildRoleSectionHeader(
    BuildContext context,
    String title,
    String roleKey,
  ) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: paddingSmall),
      margin: EdgeInsets.only(bottom: paddingSmall),
      child: Row(children: [Expanded(child: TitleWidget(title: title))]),
    );
  }

  /// Builds a message for when a role has no data
  Widget _buildEmptyDataMessage(BuildContext context, String role) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(paddingMedium),
      decoration: BoxDecoration(
        color: Color.fromRGBO(128, 128, 128, 0.1), // Light grey
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Color.fromRGBO(128, 128, 128, 0.3),
        ), // Darker grey
      ),
      child: Column(
        children: [
          Utils.cachedSvgWrapper(
            'icon/illustration-empty-doc-check.svg',
            width: Get.width / 4,
          ),
          SizedBox(height: paddingMedium),
          Text(
            'Tidak ada data untuk $role',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Builds the NET APE summary card
  Widget _buildNetApeSummary(
    BuildContext context,
    List<WidgetProductionDetailModels> data, {
    String? roleKey,
  }) {
    // Calculate total NET APE based on role
    num totalNetApe = 0;

    // If we're in the Area tab and a roleKey is provided, use the role-specific data
    if (controller.selectedType.value == kSwitchProdArea && roleKey != null) {
      switch (roleKey) {
        case kUserLevelABDD:
          totalNetApe = controller.calculateTotalNetApe(
            controller.getAbddData(),
          );
          break;
        case kUserLevelBdm:
          totalNetApe = controller.calculateTotalNetApe(
            controller.getBdmData(),
          );
          break;
        case kUserLevelBDD:
          totalNetApe = controller.calculateTotalNetApe(
            controller.getBddData(),
          );
          break;
        case kUserLevelHOS:
          totalNetApe = controller.calculateTotalNetApe(
            controller.getHosData(),
          );
          break;
        default:
          // If no specific role, calculate from the provided data
          totalNetApe = controller.calculateTotalNetApe(data);
      }
    } else {
      // For other tabs, calculate from the provided data
      totalNetApe = controller.calculateTotalNetApe(data);
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(paddingMedium),
      margin: EdgeInsets.only(top: paddingSmall),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
        borderRadius: BorderRadius.circular(16),
      ),
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: 'Net APE : ',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color:
                    Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
              ),
            ),
            TextSpan(
              text:
                  'Rp${Utils.currencyFormatters(data: totalNetApe.toString(), currency: '')}',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the ABDD table with Area, Region, Territory, and Net APE columns
  Widget _buildAbddTable(
    BuildContext context,
    List<WidgetProductionDetailModels> areaData,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Table(
            border: TableBorder(
              horizontalInside: BorderSide(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                width: 1,
              ),
            ),
            defaultColumnWidth: IntrinsicColumnWidth(),
            children: [
              // Header row
              TableRow(
                decoration: BoxDecoration(color: kColorPaninBlue),
                children: [
                  _buildTableHeaderCell(context, 'Area'),
                  _buildTableHeaderCell(context, 'Region'),
                  _buildTableHeaderCell(context, 'Teritori'),
                  _buildTableHeaderCell(context, 'Net APE'),
                ],
              ),
              // Data rows from API
              ...areaData.map(
                (data) => _buildTableDataRowFromModel(context, data),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the BDM table with Area, Region, Territory, and Net APE columns
  Widget _buildBdmTable(
    BuildContext context,
    List<WidgetProductionDetailModels> areaData,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Table(
            border: TableBorder(
              horizontalInside: BorderSide(
                color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
                width: 1,
              ),
            ),
            defaultColumnWidth: IntrinsicColumnWidth(),
            children: [
              // Header row
              TableRow(
                decoration: BoxDecoration(color: kColorPaninBlue),
                children: [
                  _buildTableHeaderCell(context, 'Area'),
                  _buildTableHeaderCell(context, 'Region'),
                  _buildTableHeaderCell(context, 'Teritori'),
                  _buildTableHeaderCell(context, 'Net APE'),
                ],
              ),
              // Data rows from API
              ...areaData.map(
                (data) => _buildTableDataRowFromModel(context, data),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the BDD table with only Region, Territory, and Net APE
  Widget _buildBddTable(
    BuildContext context,
    List<WidgetProductionDetailModels> areaData,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Table(
          border: TableBorder(
            horizontalInside: BorderSide(
              color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              width: 1,
            ),
          ),
          columnWidths: const {
            0: FlexColumnWidth(1.2),
            1: FlexColumnWidth(1.2),
            2: FlexColumnWidth(1.5),
          },
          children: [
            // Header row
            TableRow(
              decoration: BoxDecoration(color: kColorPaninBlue),
              children: [
                _buildTableHeaderCell(context, 'Region'),
                _buildTableHeaderCell(context, 'Teritori'),
                _buildTableHeaderCell(context, 'Net APE'),
              ],
            ),
            // Data rows from API
            ...areaData.map(
              (data) => TableRow(
                children: [
                  _buildTableDataCell(context, data.bddRegion ?? '-'),
                  _buildTableDataCell(context, data.territory ?? '-'),
                  _buildTableDataCell(
                    context,
                    Utils.currencyFormatters(
                      data: (data.netApe ?? 0).toString(),
                      currency: '',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the HOS table with only Territory and Net APE
  Widget _buildHosTable(
    BuildContext context,
    List<WidgetProductionDetailModels> areaData,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Table(
          border: TableBorder(
            horizontalInside: BorderSide(
              color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
              width: 1,
            ),
          ),
          columnWidths: const {
            0: FlexColumnWidth(1.5),
            1: FlexColumnWidth(1.5),
          },
          children: [
            // Header row
            TableRow(
              decoration: BoxDecoration(color: kColorPaninBlue),
              children: [
                _buildTableHeaderCell(context, 'Teritori'),
                _buildTableHeaderCell(context, 'Net APE'),
              ],
            ),
            // Data rows from API
            ...areaData.map(
              (data) => TableRow(
                children: [
                  _buildTableDataCell(context, data.territory ?? '-'),
                  _buildTableDataCell(
                    context,
                    Utils.currencyFormatters(
                      data: (data.netApe ?? 0).toString(),
                      currency: '',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a data row for the table from a WidgetProductionDetailModels
  TableRow _buildTableDataRowFromModel(
    BuildContext context,
    WidgetProductionDetailModels data,
  ) {
    return TableRow(
      children: [
        _buildTableDataCell(context, data.area ?? '-'),
        _buildTableDataCell(context, data.bddRegion ?? '-'),
        _buildTableDataCell(context, data.territory ?? '-'),
        _buildTableDataCell(
          context,
          Utils.currencyFormatters(
            data: (data.netApe ?? 0).toString(),
            currency: '',
          ),
        ),
      ],
    );
  }

  /// Builds a header cell for the table
  Widget _buildTableHeaderCell(BuildContext context, String text) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: paddingMedium,
        horizontal: paddingSmall,
      ),
      child: Text(
        text,
        textAlign: TextAlign.center,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Builds a data cell for the table
  Widget _buildTableDataCell(BuildContext context, String text) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: paddingMedium,
        horizontal: paddingSmall,
      ),
      child: Text(
        text,
        textAlign: TextAlign.center,
        style: Theme.of(context).textTheme.bodyMedium,
      ),
    );
  }

  /// Builds a card for individual policy data
  Widget _contentCard(
    BuildContext context, {
    required WidgetProductionDetailModels data,
    required int index,
    required int totalItems,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: paddingMedium),
      margin: EdgeInsets.only(top: paddingSmall),
      width: Get.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  data.policyHolderName ?? '-',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              _buildStatusBadge(context, data.status),
            ],
          ),
          Text(
            data.policyNo ?? '-',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(height: 2),
          ),
          _buildNetApeAmount(context, data.netApe, isSmall: true),
          SizedBox(height: paddingSmall),
          _buildDividerIfNeeded(index, totalItems),
        ],
      ),
    );
  }

  /// Builds a card for team/group data
  Widget _teamContentCard(
    BuildContext context, {
    required WidgetProductionDetailModels data,
    required int index,
    required int totalItems,
  }) {
    // Check if we're in the group tab
    final isGroupTab =
        controller.selectedType.value == kSwitchProdGroup ||
        (Get.arguments[kArgsUserLevel] == kUserLevelBd &&
            controller.selectedType.value == kSwitchProdTeam);

    return GestureDetector(
      onTap: isGroupTab ? () => _navigateToDetailPage(data) : null,
      child: Container(
        color: Colors.transparent,
        padding: EdgeInsets.symmetric(horizontal: paddingMedium),
        margin: EdgeInsets.only(top: paddingSmall),
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                controller.selectedType.value == kSwitchProdBranch
                    ? Container()
                    : CircleAvatar(),
                SizedBox(width: paddingSmall),
                Expanded(
                  child: Obx(
                    () => Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // if (controller.selectedType.value != kSwitchProdBranch)
                        Text(
                          '${data.agentLevel} ${data.agentName ?? '-'}${(controller.userLevel.inList([kUserLevelBd, kUserLevelBm]) && controller.agentName == data.agentName) ? " (Saya)" : ""}',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w700),
                          overflow: TextOverflow.ellipsis,
                        ),
                        controller.selectedType.value == kSwitchProdBranch
                            ? Text(
                              data.mainBranchCode ?? '-',
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w700),
                              overflow: TextOverflow.ellipsis,
                            )
                            : Text(
                              data.agentCode ?? '-',
                              style: Theme.of(
                                context,
                              ).textTheme.bodyMedium?.copyWith(
                                color:
                                    Get.isDarkMode
                                        ? kColorTextTersier
                                        : kColorTextTersierLight,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                        _buildNetApeAmount(context, data.netApe),
                      ],
                    ),
                  ),
                ),
                if (isGroupTab)
                  Icon(
                    Icons.chevron_right,
                    color: Get.isDarkMode ? kColorTextDark : kColorTextLight,
                  ),
              ],
            ),
            _buildDividerIfNeeded(index, totalItems),
          ],
        ),
      ),
    );
  }

  /// Navigate to the detail page for a specific agent
  void _navigateToDetailPage(WidgetProductionDetailModels data) {
    if (data.agentCode == null) return;

    Get.toNamed(
      '/production-detail',
      arguments: {
        kFieldagentCode: data.agentCode,
        kArgsProductionType: controller.selectedMonth.value,
      },
    );
  }

  /// Builds a status badge with appropriate colors
  Widget _buildStatusBadge(BuildContext context, String? status) {
    final isActive = status?.toLowerCase() == 'aktif';
    final backgroundColor =
        isActive ? kColorGlobalBgGreen : kColorGlobalBgWarning;
    final textColor = isActive ? kColorGlobalGreen : kColorGlobalWarning;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: paddingSmall, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        status ?? '-',
        style: Theme.of(
          context,
        ).textTheme.bodyMedium?.copyWith(color: textColor),
      ),
    );
  }

  /// Builds the NET APE amount text
  Widget _buildNetApeAmount(
    BuildContext context,
    num? amount, {
    bool isSmall = false,
  }) {
    final formattedAmount = Utils.currencyFormatters(
      data: ((amount ?? 0).toString()),
      currency: '',
    );

    return Text(
      'NET APE $formattedAmount',
      style: (isSmall
              ? Theme.of(context).textTheme.bodySmall
              : Theme.of(context).textTheme.bodyMedium)
          ?.copyWith(
            fontWeight: FontWeight.w700,
            height: isSmall ? 1 : null,
            color: Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
          ),
      overflow: TextOverflow.ellipsis,
    );
  }

  /// Builds a divider if not the last item
  Widget _buildDividerIfNeeded(int index, int totalItems) {
    return index != (totalItems - 1)
        ? Divider()
        : SizedBox(height: paddingSmall);
  }

  /// Builds the tab switcher container based on user level
  Widget _switchContainer(BuildContext context, {String? level}) {
    // Don't show tabs for BP user level
    if (level == kUserLevelBp) {
      return Container();
    }

    // Check if it's a standard user level (BM, BD)
    final isStandardUserLevel = level == kUserLevelBm || level == kUserLevelBd;

    return Container(
      width: Get.width,
      padding: EdgeInsets.all(paddingSmall),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(50),
      ),
      child:
          isStandardUserLevel
              ? _buildStandardUserLevelTabs(context, level)
              : _buildOtherUserLevelTabs(context),
    );
  }

  /// Builds tabs for standard user levels (BM, BD)
  Widget _buildStandardUserLevelTabs(BuildContext context, String? level) {
    return Row(
      children: [
        _buildSwitchTab(
          context,
          onTap: () => controller.selectedType.value = kSwitchProdIndividu,
          title: 'Individu',
          key: kSwitchProdIndividu,
        ),
        SizedBox(width: paddingSmall),
        _buildSwitchTab(
          context,
          onTap: () => controller.selectedType.value = kSwitchProdTeam,
          title: level == kUserLevelBd ? 'Group' : 'Team',
          key: kSwitchProdTeam,
        ),
      ],
    );
  }

  /// Builds tabs for other user levels (BDM, CAO, etc.)
  Widget _buildOtherUserLevelTabs(BuildContext context) {
    return Row(
      children: [
        _buildSwitchTab(
          context,
          onTap: () => controller.selectedType.value = kSwitchProdGroup,
          title: 'Group',
          key: kSwitchProdGroup,
        ),
        SizedBox(width: paddingSmall),
        _buildSwitchTab(
          context,
          onTap: () => controller.selectedType.value = kSwitchProdBranch,
          title: 'Cabang',
          key: kSwitchProdBranch,
        ),
        SizedBox(width: paddingSmall),
        _buildSwitchTab(
          context,
          onTap: () => controller.selectedType.value = kSwitchProdArea,
          title: 'Area',
          key: kSwitchProdArea,
        ),
      ],
    );
  }

  /// Builds the header with title and period dropdown
  Widget _header(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Row(
        children: [
          Expanded(child: TitleWidget(title: 'Produksi Saya')),
          _buildPeriodDropdown(context),
        ],
      ),
    );
  }

  /// Builds the period dropdown (monthly/yearly)
  Widget _buildPeriodDropdown(BuildContext context) {
    // Define dropdown theme
    final dropdownTheme = Theme.of(context).copyWith(
      inputDecorationTheme: const InputDecorationTheme(
        errorBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        focusedErrorBorder: InputBorder.none,
        disabledBorder: InputBorder.none,
        enabledBorder: InputBorder.none,
        isDense: true,
      ),
    );

    // Define dropdown decoration
    final dropdownDecoration = CustomDropdownDecoration(
      closedBorderRadius: BorderRadius.circular(8),
      expandedBorderRadius: BorderRadius.circular(8),
      closedFillColor: Theme.of(context).colorScheme.surface,
      expandedFillColor: Theme.of(context).colorScheme.surface,
      closedBorder: Border.all(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
      ),
      expandedBorder: Border.all(
        color: Get.isDarkMode ? kColorBorderDark : kColorBorderLight,
      ),
      listItemStyle: Theme.of(context).textTheme.bodyMedium,
      hintStyle: Theme.of(context).textTheme.bodyMedium,
      headerStyle: Theme.of(context).textTheme.bodyMedium,
    );

    return Theme(
      data: dropdownTheme,
      child: SizedBox(
        width: 150,
        child: CustomDropdown(
          items: const ['Perbulan', 'Pertahun'],
          initialItem:
              controller.selectedMonth.value == kSwitchMonthly
                  ? "Perbulan"
                  : "Pertahun",
          onChanged: _handlePeriodChange,
          closedHeaderPadding: EdgeInsets.all(paddingSmall),
          decoration: dropdownDecoration,
        ),
      ),
    );
  }

  /// Handles period dropdown change
  void _handlePeriodChange(String? val) {
    if (val == null) return;

    if (val.toLowerCase() == 'perbulan') {
      controller.selectedMonth.value = kSwitchMonthly;
    } else {
      controller.selectedMonth.value = kSwitchYearly;
    }
  }

  /// Builds a single tab in the switch container
  Widget _buildSwitchTab(
    BuildContext context, {
    required Function() onTap,
    required String title,
    required String key,
  }) {
    return Expanded(
      child: Obx(() {
        final isSelected = controller.selectedType.value == key;
        return GestureDetector(
          onTap: () {
            // Only change the tab if it's not already selected
            if (!isSelected) {
              onTap();
              controller.refreshDataIfNeeded();
            }
          },
          child: Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingSmall),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
              border: isSelected ? Border.all(color: kColorBgLight) : null,
              color: isSelected ? Theme.of(context).colorScheme.surface : null,
            ),
            child: Text(
              title,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        );
      }),
    );
  }
}
