// ignore_for_file: constant_identifier_names

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/pages/authentication/first_login/change_password_success.dart';
import 'package:pdl_superapp/pages/authentication/first_login/first_login_success_page.dart';
import 'package:pdl_superapp/pages/authentication/forgot_password.dart';
import 'package:pdl_superapp/pages/authentication/forgot_password_sent.dart';
import 'package:pdl_superapp/pages/authentication/login_page.dart';
import 'package:pdl_superapp/pages/authentication/first_login/reset_password_page.dart';
import 'package:pdl_superapp/pages/authentication/verification_expired.dart';
import 'package:pdl_superapp/pages/dummy_page.dart';
import 'package:pdl_superapp/pages/error_page.dart';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/main_navigator.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/recruitment_form_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/public_recruitment_form_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/photo_page.dart';
import 'package:pdl_superapp/middlewares/auth_middleware.dart';
import 'package:pdl_superapp/pages/public/public_landing_page.dart';
import 'package:pdl_superapp/pages/test/test_access_page.dart';
import 'package:pdl_superapp/pages/menu/inbox/detail_inbox_page.dart';
import 'package:pdl_superapp/pages/menu/inbox/inbox_list_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/keagenan_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/photo_image_cropper_page.dart';
import 'package:pdl_superapp/pages/menu/keagenan/photo_page_panduan.dart';
import 'package:pdl_superapp/pages/menu/keagenan/recruitment/recruitment_list_page.dart';
import 'package:pdl_superapp/pages/rejoin/requested_rejoin_list_page.dart';
import 'package:pdl_superapp/pages/rejoin/detail_rejoin_page.dart';
import 'package:pdl_superapp/pages/rejoin/rejoin_list_page.dart';
import 'package:pdl_superapp/pages/widget/page_wrapper.dart';
import 'package:pdl_superapp/pages/profile/change_password_page.dart';
import 'package:pdl_superapp/pages/profile/device_page.dart';
import 'package:pdl_superapp/pages/profile/profile_edit_page.dart';
import 'package:pdl_superapp/pages/profile/profile_page.dart';
import 'package:pdl_superapp/pages/profile/qna_page.dart';
import 'package:pdl_superapp/pages/profile/setting_page.dart';
import 'package:pdl_superapp/pages/read_page.dart';
import 'package:pdl_superapp/pages/widget/spaj_page.dart';
import 'package:pdl_superapp/pages/widget/production_widget_page.dart';
import 'package:pdl_superapp/pages/widget/production_detail_widget_page.dart';
import 'package:pdl_superapp/pages/widget/kompensasi_page.dart';
import 'package:pdl_superapp/pages/widget/claim_page.dart';
import 'package:pdl_superapp/pages/widget/polis_lapse_page.dart';
import 'package:pdl_superapp/pages/widget/polis_jatuh_tempo_page.dart';
import 'package:pdl_superapp/pages/widget/birthday_page.dart';
import 'package:pdl_superapp/pages/home/<USER>';

abstract class Routes {
  Routes._();

  static const MAIN = '/main';
  static const HOME = '/home';
  static const LOGIN = '/login';
  static const FIRST_LOGIN_SUCCESS = '/login-success-first';
  static const RESET_PASSWORD = '/new-password';
  static const CHANGE_PASSWORD_SUCCESS = '/change-password-success';
  static const FORGOT_PASSWORD = '/forget-password';
  static const FORGOT_PASSWORD_SENT = '/forgot-password-sent';
  static const VERIFICATION_EXPIRED = '/verification-expired';
  static const READ_PAGE = '/read';
  static const NOT_FOUND = '/404';
  // Page
  static const PROFILE = '/profile';
  static const PROFILE_EDIT = '/profile-edit';
  static const CHANGE_PASSWORD = '/change-password';
  static const SETTING = '/setting';
  static const CONNECTED_DEVICES = '/connected-devices';
  static const QNA = '/qna-page';
  // Widget Page
  static const WIDGET_MY_PRODUCTION = '/widget-my-production';
  static const PRODUCTION_DETAIL = '/production-detail';
  static const KOMPENSASI = '/kompensasi';
  static const CLAIM = '/claim';
  static const POLIS_LAPSE = '/polis-lapse';
  static const POLIS_JATUH_TEMPO = '/polis-jatuh-tempo';
  static const BIRTHDAY = '/birthday';
  static const FAVORITE = '/favorite';
  static const SPAJ = '/spaj';
  // Menu Page
  // -- Keagenan
  static const MENU_KEAGENAN = '/keagenan';
  static const KEAGENAN_LIST = '/list-keagenan';
  static const KEAGENAN_FORM = '/form-keagenan';
  static const KEAGENAN_FORM_PUBLIC = '/public/form-keagenan';
  static const PUBLIC_LANDING = '/public';
  static const TEST_ACCESS = '/test-access';
  static const PHOTO_PAGE = '/photo-page';
  static const PHOTO_PAGE_PANDUAN = '/photo-page-panduan';
  static const PHOTO_IMAGE_CROPPER = '/photo-image-cropper';
  static const KTP_PAGE = '/ktp-page';
  static const KTP_PAGE_PANDUAN = '/ktp-page-panduan';
  static const KTP_IMAGE_CROPPER = '/ktp-image-cropper';

  // --- rejoin
  static const REJOIN = '/rejoin';
  static const REJOIN_CHOOSE_USER = '/rejoin/choose';
  static const DETAIL_REJOIN = '/rejoin/detail';
  // ---

  // --- inbox
  static const MENU_INBOX = '/inbox';
  static const MENU_DETAIL_INBOX = '/inbox/detail';
  //---

  // for early developemtn
  static const DUMMYPAGE = '/dummy-page';
}

class AppPages {
  AppPages._();

  static final routes = [
    GetPage(
      name: Routes.DUMMYPAGE,
      page: () => PageWrapper(child: Dummypage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.MAIN,
      page: () => PageWrapper(child: MainNavigatorScreen()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.HOME,
      page: () => PageWrapper(child: HomePage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.PROFILE,
      page: () => PageWrapper(child: ProfilePage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.PROFILE_EDIT,
      page: () => PageWrapper(child: ProfileEditPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.CHANGE_PASSWORD,
      page: () => PageWrapper(child: ChangePasswordPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.SETTING,
      page: () => PageWrapper(child: SettingPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.CONNECTED_DEVICES,
      page: () => PageWrapper(child: ConnectedDevicePage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),

    GetPage(
      name: Routes.LOGIN,
      page: () => PageWrapper(child: LoginPage()),
      transition: Transition.noTransition,
      middlewares: [LoginGuardMiddleware()],
    ),
    GetPage(
      name: Routes.FIRST_LOGIN_SUCCESS,
      page: () => PageWrapper(child: FirstLoginSuccessPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.RESET_PASSWORD,
      page: () => PageWrapper(child: ResetPasswordPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.CHANGE_PASSWORD_SUCCESS,
      page: () => PageWrapper(child: ChangePasswordSuccessPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.FORGOT_PASSWORD,
      page: () => PageWrapper(child: ForgotPasswordPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.FORGOT_PASSWORD_SENT,
      page: () => PageWrapper(child: ForgotPasswordSentPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.VERIFICATION_EXPIRED,
      page: () => PageWrapper(child: VerificationExpiredPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.READ_PAGE,
      page: () => PageWrapper(child: ReadPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),

    GetPage(
      name: Routes.QNA,
      page: () => PageWrapper(child: QnaPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),

    GetPage(
      name: Routes.KOMPENSASI,
      page: () => PageWrapper(child: KompensasiPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.WIDGET_MY_PRODUCTION,
      page: () => PageWrapper(child: ProductionWidgetPage()),
      transition: Transition.noTransition,
      // middlewares: [
      //   PublicLayoutMiddleware(),
      //   LoginGuardMiddleware(), // Redirect to home if already logged in
      // ],
    ),
    GetPage(
      name: Routes.PRODUCTION_DETAIL,
      page: () => PageWrapper(child: ProductionDetailWidgetPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.CLAIM,
      page: () => PageWrapper(child: ClaimPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.POLIS_LAPSE,
      page: () => PageWrapper(child: PolisLapsePage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.POLIS_JATUH_TEMPO,
      page: () => PageWrapper(child: PolisJatuhTempoPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.BIRTHDAY,
      page: () => PageWrapper(child: BirthdayPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.FAVORITE,
      page: () => PageWrapper(child: FavoriteWidgetPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.SPAJ,
      page: () => PageWrapper(child: SpajPage()),
      transition: Transition.noTransition,
    ),

    // Menu
    GetPage(
      name: Routes.MENU_KEAGENAN,
      page: () => PageWrapper(child: KeagenanPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.KEAGENAN_LIST,
      page: () => PageWrapper(child: RecruitmentListPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.KEAGENAN_FORM,
      page: () => PageWrapper(child: RecruitmentFormPage()),
      transition: Transition.noTransition,
      middlewares: [AuthMiddleware()],
    ),
    GetPage(
      name: Routes.KEAGENAN_FORM_PUBLIC,
      page: () => PublicRecruitmentFormPage(),
      transition: Transition.noTransition,
      middlewares: [PublicMiddleware()],
    ),
    GetPage(
      name: Routes.PUBLIC_LANDING,
      page: () => PublicLandingPage(),
      transition: Transition.noTransition,
      middlewares: [PublicMiddleware()],
    ),
    GetPage(
      name: Routes.TEST_ACCESS,
      page: () => TestAccessPage(),
      transition: Transition.noTransition,
      middlewares: [PublicMiddleware()],
    ),
    GetPage(
      name: Routes.PHOTO_PAGE,
      page: () => PageWrapper(child: PhotoPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.PHOTO_PAGE_PANDUAN,
      page: () => PageWrapper(child: PhotoPagePanduan()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.PHOTO_IMAGE_CROPPER,
      page: () => PageWrapper(child: PhotoImageCropperPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.REJOIN,
      page: () => PageWrapper(child: RejoinListPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.REJOIN_CHOOSE_USER,
      page: () => PageWrapper(child: RequestedRejoinListPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.DETAIL_REJOIN,
      page: () => PageWrapper(child: DetailRejoinPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.MENU_KEAGENAN,
      page: () => PageWrapper(child: KeagenanPage()),
      transition: Transition.noTransition,
    ),
    GetPage(
      name: Routes.MENU_INBOX,
      page: () => PageWrapper(child: InboxListPage()),
      transition: Transition.downToUp,
    ),
    GetPage(
      name: Routes.MENU_DETAIL_INBOX,
      page: () => PageWrapper(child: DetailInboxPage()),
      transition: Transition.rightToLeft,
    ),
  ];

  static Route<dynamic> unknownRoute(RouteSettings settings) {
    return GetPageRoute(
      settings: settings,
      page: () => const ErrorPage(message: "Page not found"),
      transition: Transition.noTransition,
    );
  }
}
