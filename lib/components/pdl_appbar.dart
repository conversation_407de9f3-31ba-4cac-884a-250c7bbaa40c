import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

import '../controllers/home_widget/appbar_controller.dart';

class PdlAppbar extends StatelessWidget implements PreferredSizeWidget {
  final String? titles;
  final Color? bgColor;

  PdlAppbar({super.key, this.titles, this.bgColor});

  final controller = Get.find<ScrollAppBarController>();

  @override
  Widget build(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    return Obx(() {
      final isScrolled = controller.isScrolled.value;
      final background =
          titles != null
              ? (Get.isDarkMode ? kColorBgDark : kColorBgLight)
              : isWideScreen
              ? bgColor
              : (isScrolled ? (bgColor ?? Colors.white) : Colors.transparent);

      return AppBar(
        backgroundColor: background,
        title:
            titles != null
                ? Container(
                  width: Get.width,
                  alignment: Alignment.center,
                  child: Text(
                    titles!,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: kColorBgLight,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                )
                : Row(
                  children: [
                    SizedBox(height: 30, child: Image.asset(kIconImage)),
                    Spacer(),
                    CircleAvatar(
                      backgroundColor: kColorBgLight.withOpacity(0.35),
                    ),
                    SizedBox(width: paddingSmall),
                    CircleAvatar(
                      backgroundColor: kColorBgLight.withOpacity(0.35),
                    ),
                  ],
                ),
        flexibleSpace:
            titles == null
                ? null
                : Stack(
                  children: [
                    SizedBox(
                      width: Get.width,
                      height: 75,
                      child: Utils.cachedImageWrapper(
                        'image/img-bg-web.png',
                        fit: BoxFit.cover,
                        alignment: Alignment.topCenter,
                      ),
                    ),
                  ],
                ),
      );
    });
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight);
}
