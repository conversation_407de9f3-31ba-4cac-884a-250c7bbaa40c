import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/controllers/home_widget/home_widget_production_controller.dart';
import 'package:pdl_superapp/models/widget/widget_production_models.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';

class HomeWidgetProduction extends StatelessWidget {
  HomeWidgetProduction({super.key});

  final HomeWidgetProductionController controller = Get.put(
    HomeWidgetProductionController(),
  );

  @override
  Widget build(BuildContext context) {
    return Obx(
      () =>
          _buildProductionCard(context, userLevel: controller.userLevel.value),
    );
  }

  // Helper method to get formatted value based on selected type
  String _getFormattedValue(dynamic monthValue, dynamic yearValue) {
    final value =
        controller.selectedType.value == kSwitchMonthly
            ? monthValue
            : yearValue ?? '0.0';
    return Utils.currencyFormatters(data: value.toString());
  }

  // Helper method to calculate total from area list
  num _calculateTotalFromAreaList(
    List<WidgetProductionAreaTotalModels>? areaList,
  ) {
    if (areaList == null || areaList.isEmpty) {
      return 0;
    }

    num total = 0;
    for (var area in areaList) {
      total += area.total ?? 0;
    }
    return total;
  }

  // Common header for Net APE section
  Widget _buildNetApeHeader(BuildContext context) {
    return Text(
      'Net APE',
      style: Theme.of(
        context,
      ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700),
    );
  }

  // Common switch selector for monthly/yearly
  Widget _buildSwitchSelector(BuildContext context) {
    return Container(
      width: Get.width,
      padding: EdgeInsets.all(paddingSmall),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary,
        borderRadius: BorderRadius.circular(50),
      ),
      child: Row(
        children: [
          _buildSwitchButton(
            context,
            onTap: () => controller.selectedType.value = kSwitchMonthly,
            title:
                'Bulan${controller.userLevel.value == kUserLevelBd ? " ini" : ""}',
            key: kSwitchMonthly,
          ),
          SizedBox(width: paddingSmall),
          _buildSwitchButton(
            context,
            onTap: () => controller.selectedType.value = kSwitchYearly,
            title:
                'Tahun${controller.userLevel.value == kUserLevelBd ? " ini" : ""}',
            key: kSwitchYearly,
          ),
        ],
      ),
    );
  }

  // Unified method to build production card based on user level
  Widget _buildProductionCard(
    BuildContext context, {
    required String userLevel,
  }) {
    // Define card items based on user level
    List<Widget> cardItems = [];
    bool showSwitchSelector = false;
    double topPadding = paddingMedium;

    // Configure card based on user level
    if (userLevel == kUserLevelBp) {
      // BP level - simple month/year cards
      cardItems = [
        _buildNetApeHeader(context),
        SizedBox(height: paddingMedium),
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'Bulan ini',
            timeType: kSwitchMonthly,
            value: Utils.currencyFormatters(
              data:
                  (controller.widgetDataMonth.value.netApe?.individu ?? 0)
                      .toString(),
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        Obx(
          () => _buildProductionCardItem(
            context,
            clickSource: kSwitchYearly,
            title: 'Tahun ini',
            timeType: kSwitchYearly,
            value: Utils.currencyFormatters(
              data:
                  (controller.widgetDataYear.value.netApe?.individu ?? 0)
                      .toString(),
            ),
          ),
        ),
      ];
    } else if (userLevel == kUserLevelBm) {
      // BM level - individu and team
      showSwitchSelector = true;
      topPadding = paddingSmall;
      cardItems = [
        _buildNetApeHeader(context),
        SizedBox(height: paddingMedium),
        Obx(
          () => GestureDetector(
            onTap: () => Get.toNamed(Routes.WIDGET_MY_PRODUCTION),
            child: _buildProductionCardItem(
              context,
              title: 'Individu',
              teamType: kSwitchProdIndividu,
              value: _getFormattedValue(
                controller.widgetDataMonth.value.netApe?.individu,
                controller.widgetDataYear.value.netApe?.individu,
              ),
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'Team',
            teamType: kSwitchProdTeam,
            value: _getFormattedValue(
              controller.widgetDataMonth.value.netApe?.team,
              controller.widgetDataYear.value.netApe?.team,
            ),
          ),
        ),
      ];
    } else if (userLevel == kUserLevelBd) {
      // BD level - individu and team
      showSwitchSelector = true;
      topPadding = paddingSmall;
      cardItems = [
        _buildNetApeHeader(context),
        SizedBox(height: paddingMedium),
        Obx(
          () => GestureDetector(
            onTap: () => Get.toNamed(Routes.WIDGET_MY_PRODUCTION),
            child: _buildProductionCardItem(
              context,
              title: 'Individu',
              teamType: kSwitchProdIndividu,
              value: _getFormattedValue(
                controller.widgetDataMonth.value.netApe?.individu,
                controller.widgetDataYear.value.netApe?.individu,
              ),
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'Group',
            teamType: kSwitchProdTeam,
            value: _getFormattedValue(
              controller.widgetDataMonth.value.netApe?.group,
              controller.widgetDataYear.value.netApe?.group,
            ),
          ),
        ),
      ];
    } else if (userLevel == kUserLevelBdm) {
      // BDM level - group, branch, area
      showSwitchSelector = true;
      topPadding = paddingSmall;
      cardItems = [
        _buildNetApeHeader(context),
        SizedBox(height: paddingMedium),
        Obx(
          () => GestureDetector(
            onTap: () => Get.toNamed(Routes.WIDGET_MY_PRODUCTION),
            child: _buildProductionCardItem(
              context,
              title: 'Group',
              teamType: kSwitchProdGroup,
              value: _getFormattedValue(
                controller.widgetDataMonth.value.netApe?.group,
                controller.widgetDataYear.value.netApe?.group,
              ),
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'Cabang',
            teamType: kSwitchProdBranch,
            value: _getFormattedValue(
              controller.widgetDataMonth.value.netApe?.branch,
              controller.widgetDataYear.value.netApe?.branch,
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'Area',
            teamType: kSwitchProdArea,
            value: _getFormattedValue(
              controller.widgetDataMonth.value.netApe?.area,
              controller.widgetDataYear.value.netApe?.area,
            ),
          ),
        ),
      ];
    } else if (userLevel == kUserLevelABDD) {
      // ABDD level - show ABDD data
      showSwitchSelector = true;
      topPadding = paddingSmall;
      cardItems = [
        _buildNetApeHeader(context),
        SizedBox(height: paddingMedium),
        Obx(
          () => GestureDetector(
            onTap: () => Get.toNamed(Routes.WIDGET_MY_PRODUCTION),
            child: _buildProductionCardItem(
              context,
              title: 'Group',
              teamType: kSwitchProdGroup,
              value: _getFormattedValue(
                controller.widgetDataMonth.value.netApe?.group,
                controller.widgetDataYear.value.netApe?.group,
              ),
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'Cabang',
            teamType: kSwitchProdBranch,
            value: _getFormattedValue(
              controller.widgetDataMonth.value.netApe?.branch,
              controller.widgetDataYear.value.netApe?.branch,
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'Area',
            teamType: kSwitchProdArea,
            value: _getFormattedValue(
              controller.widgetDataMonth.value.netApe?.area,
              controller.widgetDataYear.value.netApe?.area,
            ),
          ),
        ),
      ];
    } else if (userLevel == kUserLevelBDD) {
      // BDD level - show BDD data
      showSwitchSelector = true;
      topPadding = paddingSmall;
      cardItems = [
        _buildNetApeHeader(context),
        SizedBox(height: paddingMedium),
        // BDM data
        Obx(
          () => GestureDetector(
            onTap: () => Get.toNamed(Routes.WIDGET_MY_PRODUCTION),
            child: _buildProductionCardItem(
              context,
              title: 'BDM',
              teamType: kSwitchProdGroup,
              value: _getFormattedValue(
                _calculateTotalFromAreaList(
                  controller.widgetDataMonth.value.netApe?.bdm,
                ),
                _calculateTotalFromAreaList(
                  controller.widgetDataYear.value.netApe?.bdm,
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        // ABDD data
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'ABDD',
            teamType: kSwitchProdBranch,
            value: _getFormattedValue(
              _calculateTotalFromAreaList(
                controller.widgetDataMonth.value.netApe?.abdd,
              ),
              _calculateTotalFromAreaList(
                controller.widgetDataYear.value.netApe?.abdd,
              ),
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        // BDD data
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'BDD',
            teamType: kSwitchProdArea,
            value: _getFormattedValue(
              _calculateTotalFromAreaList(
                controller.widgetDataMonth.value.netApe?.bdd,
              ),
              _calculateTotalFromAreaList(
                controller.widgetDataYear.value.netApe?.bdd,
              ),
            ),
          ),
        ),
      ];
    } else if (userLevel == kUserLevelHOS || userLevel == kUserLevelCAO) {
      // HOS/CAO level - show all data
      showSwitchSelector = true;
      topPadding = paddingSmall;
      cardItems = [
        _buildNetApeHeader(context),
        SizedBox(height: paddingMedium),
        // BDM data
        Obx(
          () => GestureDetector(
            onTap: () => Get.toNamed(Routes.WIDGET_MY_PRODUCTION),
            child: _buildProductionCardItem(
              context,
              title: 'BDM',
              teamType: kSwitchProdGroup,
              value: _getFormattedValue(
                _calculateTotalFromAreaList(
                  controller.widgetDataMonth.value.netApe?.bdm,
                ),
                _calculateTotalFromAreaList(
                  controller.widgetDataYear.value.netApe?.bdm,
                ),
              ),
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        // ABDD data
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'ABDD',
            teamType: kSwitchProdBranch,
            value: _getFormattedValue(
              _calculateTotalFromAreaList(
                controller.widgetDataMonth.value.netApe?.abdd,
              ),
              _calculateTotalFromAreaList(
                controller.widgetDataYear.value.netApe?.abdd,
              ),
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        // BDD data
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'BDD',
            teamType: kSwitchProdArea,
            value: _getFormattedValue(
              _calculateTotalFromAreaList(
                controller.widgetDataMonth.value.netApe?.bdd,
              ),
              _calculateTotalFromAreaList(
                controller.widgetDataYear.value.netApe?.bdd,
              ),
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        // HOS data
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'HOS',
            teamType: kSwitchProdArea,
            value: _getFormattedValue(
              _calculateTotalFromAreaList(
                controller.widgetDataMonth.value.netApe?.hos,
              ),
              _calculateTotalFromAreaList(
                controller.widgetDataYear.value.netApe?.hos,
              ),
            ),
          ),
        ),
      ];
    } else {
      // Default case for other roles
      showSwitchSelector = true;
      topPadding = paddingSmall;
      cardItems = [
        _buildNetApeHeader(context),
        SizedBox(height: paddingMedium),
        Obx(
          () => GestureDetector(
            onTap: () => Get.toNamed(Routes.WIDGET_MY_PRODUCTION),
            child: _buildProductionCardItem(
              context,
              title: 'Group',
              teamType: kSwitchProdGroup,
              value: _getFormattedValue(
                controller.widgetDataMonth.value.netApe?.group,
                controller.widgetDataYear.value.netApe?.group,
              ),
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'Cabang',
            teamType: kSwitchProdBranch,
            value: _getFormattedValue(
              controller.widgetDataMonth.value.netApe?.branch,
              controller.widgetDataYear.value.netApe?.branch,
            ),
          ),
        ),
        SizedBox(height: paddingMedium),
        Obx(
          () => _buildProductionCardItem(
            context,
            title: 'Area',
            teamType: kSwitchProdArea,
            value: _getFormattedValue(
              controller.widgetDataMonth.value.netApe?.area,
              controller.widgetDataYear.value.netApe?.area,
            ),
          ),
        ),
      ];
    }

    // Build the complete card with all components
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: topPadding),
        if (showSwitchSelector) ...[
          _buildSwitchSelector(context),
          SizedBox(height: paddingMedium),
        ],
        ...cardItems,

        // Add additional role-specific data sections based on user level
        Obx(() {
          final sections = _buildAdditionalRoleSections(context, userLevel);
          if (controller.state.value == ControllerState.loading) {
            return CircularProgressIndicator();
          }
          if (sections.isNotEmpty) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: sections,
            );
          }
          return Container();
        }),

        SizedBox(height: paddingExtraLarge),
        WidgetLoadMore(onTap: () => controller.load()),
        SizedBox(height: paddingSmall),
      ],
    );
  }

  // Switch button for monthly/yearly selection
  Widget _buildSwitchButton(
    BuildContext context, {
    required Function() onTap,
    required String title,
    required String key,
  }) {
    return Expanded(
      child: Obx(() {
        final isSelected = controller.selectedType.value == key;
        return GestureDetector(
          onTap: onTap,
          child: Container(
            width: Get.width,
            padding: EdgeInsets.all(paddingSmall),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(50),
              border: isSelected ? Border.all(color: kColorBgLight) : null,
              color: isSelected ? Theme.of(context).colorScheme.surface : null,
            ),
            child: Text(title, textAlign: TextAlign.center),
          ),
        );
      }),
    );
  }

  // Build additional role-specific data sections based on user level
  List<Widget> _buildAdditionalRoleSections(
    BuildContext context,
    String userLevel,
  ) {
    List<Widget> sections = [];

    // Skip if user level is BP, BM, or BD as they don't need additional sections
    if (userLevel == kUserLevelBp ||
        userLevel == kUserLevelBm ||
        userLevel == kUserLevelBd) {
      return sections;
    }

    // Add divider before additional sections
    sections.add(Divider(height: paddingExtraLarge));
    sections.add(SizedBox(height: paddingMedium));

    // BDM Section - shown for all roles except BP, BM, BD
    if (userLevel != kUserLevelBp &&
        userLevel != kUserLevelBm &&
        userLevel != kUserLevelBd) {
      sections.add(_buildRoleSectionHeader(context, 'Produksi BDM'));
      sections.add(SizedBox(height: paddingMedium));

      // Add BDM area items
      if (controller.widgetDataMonth.value.netApe?.bdm != null ||
          controller.widgetDataYear.value.netApe?.bdm != null) {
        // Get the appropriate data based on selected type
        final bdmData =
            controller.selectedType.value == kSwitchMonthly
                ? controller.widgetDataMonth.value.netApe?.bdm
                : controller.widgetDataYear.value.netApe?.bdm;

        if (bdmData != null && bdmData.isNotEmpty) {
          for (var area in bdmData) {
            sections.add(
              _buildRoleDataItem(
                context,
                title: area.area ?? 'Area',
                value: Utils.currencyFormatters(
                  data: (area.total ?? 0).toString(),
                ),
                teamType: kSwitchProdGroup,
              ),
            );
            sections.add(SizedBox(height: paddingSmall));
          }
        } else {
          sections.add(
            _buildRoleDataItem(
              context,
              title: 'BDM',
              value: _getFormattedValue(
                _calculateTotalFromAreaList(
                  controller.widgetDataMonth.value.netApe?.bdm,
                ),
                _calculateTotalFromAreaList(
                  controller.widgetDataYear.value.netApe?.bdm,
                ),
              ),
              teamType: kSwitchProdGroup,
            ),
          );
        }
      }
    }

    // ABDD Section - shown for ABDD, BDD, HOS, and CAO roles
    if (userLevel == kUserLevelABDD ||
        userLevel == kUserLevelBDD ||
        userLevel == kUserLevelHOS ||
        userLevel == kUserLevelCAO) {
      sections.add(SizedBox(height: paddingMedium));
      sections.add(_buildRoleSectionHeader(context, 'Produksi ABDD'));
      sections.add(SizedBox(height: paddingMedium));

      // Add ABDD area items
      if (controller.widgetDataMonth.value.netApe?.abdd != null ||
          controller.widgetDataYear.value.netApe?.abdd != null) {
        // Get the appropriate data based on selected type
        final abddData =
            controller.selectedType.value == kSwitchMonthly
                ? controller.widgetDataMonth.value.netApe?.abdd
                : controller.widgetDataYear.value.netApe?.abdd;

        if (abddData != null && abddData.isNotEmpty) {
          for (var area in abddData) {
            sections.add(
              _buildRoleDataItem(
                context,
                title: area.area ?? 'Area',
                value: Utils.currencyFormatters(
                  data: (area.total ?? 0).toString(),
                ),
                teamType: kSwitchProdBranch,
              ),
            );
            sections.add(SizedBox(height: paddingSmall));
          }
        } else {
          sections.add(
            Obx(
              () => _buildRoleDataItem(
                context,
                title: 'ABDD',
                value: _getFormattedValue(
                  _calculateTotalFromAreaList(
                    controller.widgetDataMonth.value.netApe?.abdd,
                  ),
                  _calculateTotalFromAreaList(
                    controller.widgetDataYear.value.netApe?.abdd,
                  ),
                ),
                teamType: kSwitchProdBranch,
              ),
            ),
          );
        }
      }
    }

    // BDD Section - shown for BDD, HOS, and CAO roles
    if (userLevel == kUserLevelBDD ||
        userLevel == kUserLevelHOS ||
        userLevel == kUserLevelCAO) {
      sections.add(SizedBox(height: paddingMedium));
      sections.add(_buildRoleSectionHeader(context, 'Produksi BDD'));
      sections.add(SizedBox(height: paddingMedium));

      // Add BDD area items
      if (controller.widgetDataMonth.value.netApe?.bdd != null ||
          controller.widgetDataYear.value.netApe?.bdd != null) {
        // Get the appropriate data based on selected type
        final bddData =
            controller.selectedType.value == kSwitchMonthly
                ? controller.widgetDataMonth.value.netApe?.bdd
                : controller.widgetDataYear.value.netApe?.bdd;

        if (bddData != null && bddData.isNotEmpty) {
          for (var area in bddData) {
            sections.add(
              _buildRoleDataItem(
                context,
                title: area.area ?? 'Region',
                value: Utils.currencyFormatters(
                  data: (area.total ?? 0).toString(),
                ),
                teamType: kSwitchProdArea,
              ),
            );
            sections.add(SizedBox(height: paddingSmall));
          }
        } else {
          sections.add(
            _buildRoleDataItem(
              context,
              title: 'BDD',
              value: _getFormattedValue(
                _calculateTotalFromAreaList(
                  controller.widgetDataMonth.value.netApe?.bdd,
                ),
                _calculateTotalFromAreaList(
                  controller.widgetDataYear.value.netApe?.bdd,
                ),
              ),
              teamType: kSwitchProdArea,
            ),
          );
        }
      }
    }

    // HOS Section - shown for HOS and CAO roles
    if (userLevel == kUserLevelHOS || userLevel == kUserLevelCAO) {
      sections.add(SizedBox(height: paddingMedium));
      sections.add(_buildRoleSectionHeader(context, 'Produksi HOS'));
      sections.add(SizedBox(height: paddingMedium));

      // Add HOS area items
      if (controller.widgetDataMonth.value.netApe?.hos != null ||
          controller.widgetDataYear.value.netApe?.hos != null) {
        // Get the appropriate data based on selected type
        final hosData =
            controller.selectedType.value == kSwitchMonthly
                ? controller.widgetDataMonth.value.netApe?.hos
                : controller.widgetDataYear.value.netApe?.hos;

        if (hosData != null && hosData.isNotEmpty) {
          for (var area in hosData) {
            sections.add(
              _buildRoleDataItem(
                context,
                title: area.area ?? 'Territory',
                value: Utils.currencyFormatters(
                  data: (area.total ?? 0).toString(),
                ),
                teamType: kSwitchProdArea,
              ),
            );
            sections.add(SizedBox(height: paddingSmall));
          }
        } else {
          sections.add(
            _buildRoleDataItem(
              context,
              title: 'HOS',
              value: _getFormattedValue(
                _calculateTotalFromAreaList(
                  controller.widgetDataMonth.value.netApe?.hos,
                ),
                _calculateTotalFromAreaList(
                  controller.widgetDataYear.value.netApe?.hos,
                ),
              ),
              teamType: kSwitchProdArea,
            ),
          );
        }
      }
    }

    return sections;
  }

  // Build a section header for role-specific data
  Widget _buildRoleSectionHeader(BuildContext context, String title) {
    return Text(
      title,
      style: Theme.of(
        context,
      ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w700),
    );
  }

  // Build a role data item with navigation
  Widget _buildRoleDataItem(
    BuildContext context, {
    required String title,
    required String value,
    String? teamType,
  }) {
    return GestureDetector(
      onTap:
          teamType != null
              ? () => Get.toNamed(
                Routes.WIDGET_MY_PRODUCTION,
                arguments: {
                  kArgsUserLevel: controller.userLevel.value,
                  kArgsProductionType:
                      controller.selectedType.value == kSwitchMonthly
                          ? kSwitchMonthly
                          : kSwitchYearly,
                  kArgsProductionTeamType: teamType,
                },
              )
              : null,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: paddingSmall),

        child: Row(
          children: [
            Expanded(child: Text(title)),
            Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                if (teamType != null)
                  Icon(
                    Icons.chevron_right,
                    color: Theme.of(context).colorScheme.primary,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Production card item with value and navigation
  Widget _buildProductionCardItem(
    BuildContext context, {
    required String title,
    required String value,
    String? timeType,
    String? teamType,
    String? clickSource,
  }) {
    return GestureDetector(
      onTap:
          () => Get.toNamed(
            Routes.WIDGET_MY_PRODUCTION,
            arguments: {
              kArgsUserLevel: controller.userLevel.value,
              kArgsProductionType:
                  clickSource ??
                  (controller.selectedType.value == kSwitchMonthly
                      ? kSwitchMonthly
                      : kSwitchYearly),
              kArgsProductionTeamType: teamType,
            },
          ),
      child: Container(
        color: Colors.transparent,
        child: Row(
          children: [
            Expanded(child: Text(title)),
            Wrap(
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                Icon(
                  Icons.chevron_right,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
