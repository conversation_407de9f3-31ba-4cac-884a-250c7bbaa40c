import 'package:flutter/material.dart';
import 'package:pdl_superapp/components/custom_chip.dart';
import 'package:pdl_superapp/components/pdl_bottom_sheet.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/keys.dart';

class ClaimItem extends StatelessWidget {
  final String claimId;
  final String policyHolder;
  final String policyNumber;
  final String issueDate;
  final String claimAmount;
  final String note;
  final List<String> bulletNotes;
  final String status;
  final String agentName;
  final String agentCode;
  final String userLevel;

  const ClaimItem({
    super.key,
    required this.claimId,
    required this.policyHolder,
    required this.policyNumber,
    required this.issueDate,
    required this.claimAmount,
    required this.note,
    required this.bulletNotes,
    required this.status,
    required this.agentName,
    required this.agentCode,
    required this.userLevel,
  });

  @override
  Widget build(BuildContext context) {
    final BuildContext currentContext = context;
    CustomChip chip;

    switch (status) {
      case "Proses_Pengambilan_Keputusan":
        chip = CustomChip(
          text: "Proses Pengambilan Keputusan",
          type: ChipType.warning,
        );
        break;
      case "Proses_Verifikasi":
        chip = CustomChip(text: "Proses Verifikasi", type: ChipType.warning);
        break;
      case "Pending_Dokumen_dan_Pending_Investigasi":
        chip = CustomChip(
          text: "Pending Dokumen & Investigasi",
          type: ChipType.warning,
        );
        break;
      case "Pending_Dokumen":
        chip = CustomChip(text: "Pending Dokumen", type: ChipType.warning);
        break;
      case "Pending_Investigasi":
        chip = CustomChip(text: "Pending Investigasi", type: ChipType.warning);
        break;
      case "Paid":
        chip = CustomChip(text: "Paid", type: ChipType.success);
        break;
      case "Reject":
        chip = CustomChip(text: "Reject", type: ChipType.danger);
        break;
      // Keep backward compatibility with old status values
      case "IN-CLAIM":
      case "ENDING":
        chip = CustomChip(text: status, type: ChipType.warning);
        break;
      case "CANCEL":
      case "DEATH":
      case "EXHAUSTED":
      case "LAPSE":
      case "KONVERSI":
      case "MATURED":
      case "SURRENDER":
        chip = CustomChip(text: status, type: ChipType.danger);
        break;
      case "ACTIVE":
      case "PREMIUM FREE":
        chip = CustomChip(text: status, type: ChipType.success);
        break;
      default:
        chip = CustomChip(text: status, type: ChipType.info);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (userLevel == kLevelBM)
          Container(
            height: 56,
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey,
                  ),
                  height: 44,
                  width: 44,
                ),
                SizedBox(width: 12, height: 1),
                SizedBox(
                  height: 44,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        agentName,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                      ),
                      Text(agentCode, style: TextStyle(fontSize: 12)),
                    ],
                  ),
                ),
              ],
            ),
          ),
        if (userLevel == kLevelBM) const Divider(),
        // Header with claim ID and status
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              claimId,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            chip,
          ],
        ),
        const SizedBox(height: 12),

        // Policy holder
        _buildDetailRow("Pemegang Polis", policyHolder),

        // Policy number
        _buildDetailRow("No. Polis", policyNumber),

        // Issue date
        _buildDetailRow("Tanggal Terbit", issueDate),

        // Claim amount
        _buildDetailRow("Jumlah Klaim", claimAmount),

        const SizedBox(height: 8),

        if (note.isNotEmpty || bulletNotes.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Notes section
              const Text(
                "Catatan",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),

              // Regular note or bullet points
              if (note.isNotEmpty)
                Text(
                  note,
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                ),

              // Bullet points
              if (bulletNotes.isNotEmpty)
                ...bulletNotes.map(
                  (bulletNote) => Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "• ",
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            bulletNote,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // View more button
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed:
                      () => _showNotesBottomSheet(
                        currentContext,
                        note,
                        bulletNotes,
                      ),
                  child: Text(
                    "Lihat Selengkapnya",
                    style: TextStyle(color: Colors.blue.shade400, fontSize: 15),
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(fontSize: 15, color: Colors.grey.shade600),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  // Show notes bottom sheet
  void _showNotesBottomSheet(
    BuildContext context,
    String note,
    List<String> bulletNotes,
  ) {
    PdlBottomSheet(
      title: "Catatan",
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: paddingMedium),

            // Regular note
            if (note.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(bottom: paddingMedium),
                child: Text(
                  note,
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                ),
              ),

            // Bullet points
            if (bulletNotes.isNotEmpty)
              ...bulletNotes.map(
                (bulletNote) => Padding(
                  padding: const EdgeInsets.only(top: 4.0, bottom: 4.0),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "• ",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          bulletNote,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: paddingMedium),
          ],
        ),
      ),
    );
  }
}
