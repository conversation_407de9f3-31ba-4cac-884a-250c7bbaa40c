import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

class EmptyStateView extends StatelessWidget {
  const EmptyStateView({super.key, this.msg});
  final String? msg;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: Get.width,
      child: Column(
        children: [
          Utils.cachedSvgWrapper(
            'images/img-no-content.svg',
            width: 220,
            height: 220,
          ),
          SizedBox(height: paddingLarge),
          Text(
            msg ?? 'Hmmm.. belum ada data tersedia',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:
                  Get.isDarkMode ? kColorTextTersier : kColorTextTersierLight,
            ),
          ),
          SizedBox(height: paddingExtraLarge),
        ],
      ),
    );
  }
}
