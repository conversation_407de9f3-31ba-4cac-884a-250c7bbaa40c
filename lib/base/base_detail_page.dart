import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/components/item_action_menu.dart';
import 'package:pdl_superapp/components/login/login_header.dart';
import 'package:pdl_superapp/components/pdl_button.dart';
import 'package:pdl_superapp/utils/constants.dart';
import 'package:pdl_superapp/utils/utils.dart';

// ignore: must_be_immutable
class BaseDetailPage extends StatefulWidget {
  final Widget child;
  final String title;
  final Function()? bottomAction;
  final String? bottomText;
  final BaseControllers controller;
  bool? backEnabled;
  final Function() onRefresh;
  bool? isActionActive;
  final List<Widget>? actions;
  final Widget? bottomWidget;
  final Function()? onBack;

  BaseDetailPage({
    super.key,
    required this.child,
    required this.title,
    required this.controller,
    required this.onRefresh,
    this.backEnabled,
    this.bottomAction,
    this.bottomText,
    this.isActionActive,
    this.bottomWidget,
    this.onBack,
    this.actions,
  });

  @override
  State<BaseDetailPage> createState() => _BaseDetailPageState();
}

class _BaseDetailPageState extends State<BaseDetailPage> {
  final scrollController = ScrollController();
  bool isShow = true;

  @override
  void initState() {
    super.initState();
    scrollController.addListener(() {
      setState(() {
        isShow = scrollController.offset < 75;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final bool isWideScreen = kIsWeb;
    return Scaffold(
      resizeToAvoidBottomInset: true,
      bottomNavigationBar:
          widget.bottomAction != null || widget.bottomWidget != null
              ? Container(
                width: Get.width,
                height: 70,

                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  boxShadow: [
                    BoxShadow(
                      color: Color.fromRGBO(149, 157, 165, 0.2),
                      blurRadius: 24,
                      spreadRadius: 0,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.all(paddingMedium),
                  child:
                      widget.bottomWidget ??
                      PdlButton(
                        controller: widget.controller,
                        onPressed: widget.bottomAction,
                        title: widget.bottomText ?? 'button_save'.tr,
                      ),
                ),
              )
              : null,
      body: isWideScreen ? _layoutWeb(context) : _layoutMobile(context),
    );
  }

  Widget _layoutMobile(context) {
    return Stack(
      children: [
        LoginHeader(
          child: Container(
            width: Get.width,
            height: 200,
            padding: EdgeInsets.symmetric(horizontal: paddingMedium),
            margin: EdgeInsets.only(top: 50),
            alignment: Alignment.topCenter,
            child: Row(
              children: [
                GestureDetector(
                  onTap: widget.onBack ?? () => Get.back(),
                  child: Icon(Icons.arrow_back, color: Colors.white),
                ),
                if (widget.isActionActive == true)
                  SizedBox(width: paddingSmall),
                Expanded(
                  child: Text(
                    widget.title,
                    textAlign:
                        widget.isActionActive == true ? null : TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (widget.isActionActive == true)
                  Row(
                    children: [
                      if (widget.actions != null && widget.actions!.isNotEmpty)
                        for (final widget in widget.actions!) widget,

                      //NOTE: just for example
                      if (widget.actions == null ||
                          widget.actions!.isEmpty) ...[
                        ItemActionMenu(),
                        SizedBox(width: paddingSmall),
                        ItemActionMenu(),
                      ],
                    ],
                  ),
                if (widget.isActionActive != true)
                  Icon(Icons.arrow_back, color: Colors.transparent),
              ],
            ),
          ),
        ),
        SizedBox(
          width: Get.width,
          height: Get.height,
          child: Column(
            children: [
              SizedBox(height: 100),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async => widget.onRefresh(),
                  child: Container(
                    height: Get.height,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius:
                          isShow
                              ? BorderRadius.only(
                                topLeft: Radius.circular(20),
                                topRight: Radius.circular(20),
                              )
                              : null,
                    ),
                    child: SingleChildScrollView(
                      controller: scrollController,
                      physics: AlwaysScrollableScrollPhysics(),
                      child: widget.child,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: Get.width, height: Get.height),
      ],
    );
  }

  Widget _layoutWeb(context) {
    return Stack(
      children: [
        Positioned.fill(
          child: Utils.cachedImageWrapper(
            'image/img-bg-web.png',
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(
          width: Get.width,
          height: Get.height,
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  vertical: paddingLarge,
                  horizontal: paddingLarge,
                ),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap:
                          widget.backEnabled == true ? () => Get.back() : null,
                      child: Icon(
                        Icons.arrow_back,
                        color:
                            widget.backEnabled == true
                                ? Colors.white
                                : Colors.transparent,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        widget.title,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                    Icon(Icons.arrow_back, color: Colors.transparent),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  color: Theme.of(context).colorScheme.surface,
                  width: Get.width,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        SizedBox(width: Get.width / 1.5, child: widget.child),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
