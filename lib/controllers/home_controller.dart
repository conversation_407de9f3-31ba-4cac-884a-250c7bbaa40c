import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:pdl_superapp/utils/firestore_services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/components/home/<USER>';
import 'package:pdl_superapp/models/user_models.dart';

import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>';
import 'package:pdl_superapp/pages/home/<USER>/kompensasi_widget.dart';
import 'package:pdl_superapp/pages/home/<USER>/validasi_widget.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:showcaseview/showcaseview.dart';

class HomeController extends BaseControllers {
  // declare global key for tutorial use
  GlobalKey keyOne = GlobalKey();
  GlobalKey keyTwo = GlobalKey();
  GlobalKey keyThree = GlobalKey();

  late SharedPreferences prefs;

  // for UI
  Rx<UserModels> userData = Rx(UserModels());

  // Favorite widgets
  final RxList<String> favoriteWidgetIds = RxList();
  final RxList<Widget> favoriteWidgets = RxList();

  RxString userLevel = ''.obs;

  // Loading state for UI
  @override
  RxBool isLoading = true.obs;

  @override
  void onInit() async {
    super.onInit();
    isLoading.value = true; // Set loading to true on initialization

    if (!kIsWeb) {
      await Utils.checkForUpdates();
    }
    prefs = await SharedPreferences.getInstance();
    api.getWidgetSort(controllers: this);
    loadFavoriteWidgets();
    userLevel.value = prefs.getString(kStorageUserLevelComplete) ?? '';
    // register/update current devices
    await registerDevice();
    await api.getProfile(controllers: this, code: kReqGetProfile, debug: true);
  }

  startShowCase(context) {
    ShowCaseWidget.of(context).startShowCase([keyOne, keyTwo, keyThree]);
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    switch (requestCode) {
      case kReqAddDevices:
        break;
      case kReqGetProfile:
        // parse data user
        parseData(response);
        // isLoading.value is set to false in parseData
        break;
      default:
        isLoading.value =
            false; // Ensure loading is set to false for other cases
    }
  }

  @override
  load() async {
    super.load();
    setLoading(true);
    if (!kIsWeb) {
      await Utils.checkForUpdates();
    }
    isLoading.value = true; // Set loading to true before API call
    try {
      api.getWidgetSort(controllers: this);
      // Load profile data
      checkForFavoriteWidgetChanges();
      await api.getProfile(
        controllers: this,
        code: kReqGetProfile,
        debug: true,
      );
      // Force reload favorite widgets from Firestore
      await _forceReloadFavoriteWidgets();
    } catch (e) {
      log('Error loading profile: $e');
      isLoading.value =
          false; // Ensure loading is set to false if API call fails
    }
  }

  // Force reload favorite widgets from Firestore
  Future<void> _forceReloadFavoriteWidgets() async {
    try {
      // Clear the last known list to force a reload
      _lastKnownFavoriteIds.clear();

      // Load favorite widgets
      loadFavoriteWidgets();
    } catch (e) {
      log('Error reloading favorite widgets: $e');
    }
  }

  // Store the last known list of favorite widget IDs for comparison
  List<String> _lastKnownFavoriteIds = [];

  // Check if favorite widgets have changed in SharedPreferences
  void checkForFavoriteWidgetChanges() {
    try {
      List<String> currentIds = prefs.getStringList('favorite_widgets') ?? [];

      // Check if the list has changed
      if (!_areListsEqual(_lastKnownFavoriteIds, currentIds)) {
        // Update the last known list
        _lastKnownFavoriteIds = List.from(currentIds);

        // Reload the widgets
        loadFavoriteWidgets();
      }
    } catch (e) {
      log('Error checking for favorite widget changes: $e');
    }
  }

  // Helper method to compare two lists
  bool _areListsEqual(List<String> list1, List<String> list2) {
    if (list1.length != list2.length) return false;
    for (int i = 0; i < list1.length; i++) {
      if (list1[i] != list2[i]) return false;
    }
    return true;
  }

  // Load favorite widgets from Firestore and SharedPreferences
  void loadFavoriteWidgets() async {
    try {
      // First try to get from Firestore
      List<String>? firestoreIds = await _loadFromFirestore();

      // If Firestore data is available, use it
      if (firestoreIds != null && firestoreIds.isNotEmpty) {
        log('Using favorite widgets from Firestore');

        // Also update SharedPreferences to keep them in sync
        await prefs.setStringList('favorite_widgets', firestoreIds);

        _updateFavoriteWidgets(firestoreIds);
        return;
      }

      // Otherwise, fall back to SharedPreferences
      log('Falling back to SharedPreferences for favorite widgets');
      List<String> savedIds = prefs.getStringList('favorite_widgets') ?? [];

      // If no saved favorites, use default (first 4 widgets)
      if (savedIds.isEmpty) {
        savedIds = ['1', '2', '3', '4']; // Default to first 4 widgets

        // Save defaults to SharedPreferences
        await prefs.setStringList('favorite_widgets', savedIds);
      }

      // Update widgets with the local data
      _updateFavoriteWidgets(savedIds);
    } catch (e) {
      log('Error loading favorite widgets: $e');

      // In case of error, try to use local data
      List<String> savedIds = prefs.getStringList('favorite_widgets') ?? [];
      if (savedIds.isEmpty) {
        savedIds = ['1', '2', '3', '4']; // Default to first 4 widgets

        // Save defaults to SharedPreferences
        await prefs.setStringList('favorite_widgets', savedIds);
      }
      _updateFavoriteWidgets(savedIds);
    }
  }

  // Update favorite widgets with the given IDs
  void _updateFavoriteWidgets(List<String> ids) {
    // Update the last known list
    _lastKnownFavoriteIds = List.from(ids);

    // Update the favoriteWidgetIds list
    favoriteWidgetIds.value = ids;

    // Generate widgets based on IDs
    generateFavoriteWidgets();
  }

  // Load favorite widgets from Firestore
  Future<List<String>?> _loadFromFirestore() async {
    try {
      // Get FirestoreServices instance
      FirestoreServices firestoreServices;
      try {
        firestoreServices = Get.find<FirestoreServices>();
      } catch (e) {
        log('FirestoreServices not found, creating new instance');
        firestoreServices = Get.put(FirestoreServices());
      }

      // Get favorite widgets from Firestore
      return await firestoreServices.getFavoriteWidgets();
    } catch (e) {
      log('Error loading from Firestore: $e');
      return null;
    }
  }

  // Generate HomeWidgetBase widgets based on favoriteWidgetIds
  void generateFavoriteWidgets() {
    favoriteWidgets.clear();

    // Map of widget IDs to their corresponding widgets
    Map<String, Widget> widgetMap = {
      '1': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-ultah-nasabah.svg',
        title: 'Ulang Tahun Nasabah',
        widgetKey: kWidgetKeyUlangTahunNasabah,
        content: BirthdayWidget(),
      ),
      '2': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-status-klaim.svg',
        title: 'Status Klaim',
        widgetKey: kWidgetKeyStatusKlaim,
        content: ClaimWidget(),
      ),
      '3': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-polis-lapse.svg',
        title: 'Polis Lapse',
        widgetKey: kWidgetKeyPolisLapse,
        content: PolisLapsedWidget(),
      ),
      '4': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-polis jatuh-tempo.svg',
        title: 'Polis Jatuh Tempo',
        widgetKey: kWidgetKeyPolisJatuhTempo,
        content: PolisJatuhTempoWidget(),
      ),
      '5': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-produksi-saya.svg',
        title: 'Produksi Saya',
        widgetKey: kWidgetKeyProduksiSaya,
        content: HomeWidgetProduction(),
      ),
      '6': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-validasi.svg',
        title: 'Validasi & Promosi',
        widgetKey: kWidgetKeyValidasiPromosi,
        content: ValidasiWidget(),
      ),
      '7': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-estimasi.svg',
        title: 'Estimasi Kompensasi',
        widgetKey: kWidgetKeyEstimasiKompensasi,
        content: KompensasiWidget(),
      ),
      '8': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-persistensi.svg',
        title: 'Persistensi',
        widgetKey: kWidgetKeyPersistensi,
        content: PersistensiWidget(),
      ),
      '9': HomeWidgetBase(
        iconUrl: 'icon/ic-menu-spaj.svg',
        title: 'Status SPAJ',
        widgetKey: kWidgetKeyStatusSpaj,
        content: HomeWidgetSpaj(),
      ),
    };

    // Add widgets in the order specified by favoriteWidgetIds
    log('Generating ${favoriteWidgetIds.length} favorite widgets');
    for (String id in favoriteWidgetIds) {
      if (widgetMap.containsKey(id)) {
        log('Adding widget with ID: $id');
        favoriteWidgets.add(widgetMap[id]!);
      }
    }
  }

  parseData(response) async {
    try {
      UserModels data = UserModels.fromJson(response);
      userData.value = data;
      userLevel.value = data.roles?.code ?? '-';
      await prefs.setString(kStorageAgentName, data.agentName ?? '-');
      await prefs.setString(kStorageUserLevel, data.level ?? '-');
      await prefs.setString(kStorageUserLevelComplete, data.roles?.code ?? '-');
    } catch (e) {
      // log('Error parsing user data: $e');
    } finally {
      isLoading.value =
          false; // Ensure loading is set to false even if parsing fails
    }
  }

  @override
  void loadFailed({required int requestCode, required response}) {
    super.loadFailed(requestCode: requestCode, response: response);
    setLoading(false);
    log('Load failed for request code: $requestCode');
    isLoading.value = false; // Ensure loading is set to false on failure
  }

  @override
  void loadError(e, {Response? response}) {
    super.loadError(e, response: response);
    log('Load error: $e');
    isLoading.value = false; // Ensure loading is set to false on error
  }

  performAddDevice(var data) async {
    await api.performRegisterDevice(
      controllers: this,
      data: data,
      code: kReqAddDevices,
    );
  }

  registerDevice() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    String buildNumber = await Utils.getBuildNumber();
    String appVersion = await Utils.getAppVersionOnly();
    String deviceId = '';
    var data = {};
    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await deviceInfoPlugin.webBrowserInfo;
      deviceId = '${webBrowserInfo.appCodeName}';
      data = {
        "deviceId": "${webBrowserInfo.appCodeName}",
        "deviceModel": "${webBrowserInfo.browserName}",
        "osType": "${webBrowserInfo.platform}",
        "osVersion": "${webBrowserInfo.appVersion}",
        "appVersion": appVersion,
        "appBuildNumber": buildNumber,
        "deviceLanguage": "${Get.locale}",
        "screenWidth": Get.width,
        "screenHeight": Get.height,
        "connectionType": "",
        "timezone": "",
        "firebaseToken": "",
        "manufacturer": "${webBrowserInfo.vendor}",
      };
    } else {
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfoPlugin.androidInfo;
        deviceId = androidInfo.id;
        data = {
          "deviceId": androidInfo.id,
          "deviceModel": androidInfo.model,
          "osType": Platform.localeName,
          "osVersion": androidInfo.version.release,
          "appVersion": appVersion,
          "appBuildNumber": buildNumber,
          "deviceLanguage": "${Get.locale}",
          "screenWidth": Get.width,
          "screenHeight": Get.height,
          "connectionType": "",
          "timezone": "",
          "firebaseToken": "",
          "manufacturer": androidInfo.manufacturer,
        };
      }
      if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfoPlugin.iosInfo;
        deviceId = '${iosInfo.identifierForVendor}';
        data = {
          "deviceId": "${iosInfo.identifierForVendor}",
          "deviceModel": iosInfo.modelName,
          "osType": Platform.localeName,
          "osVersion": iosInfo.systemVersion,
          "appVersion": appVersion,
          "appBuildNumber": buildNumber,
          "deviceLanguage": "${Get.locale}",
          "screenWidth": Get.width,
          "screenHeight": Get.height,
          "connectionType": "",
          "timezone": "",
          "firebaseToken": "",
          "manufacturer": "Apple",
        };
      }
    }

    performAddDevice(data);
    await prefs.setString(kDeviceId, deviceId);
  }

  void changeTheme(String name, ThemeData theme) {
    Get.changeTheme(theme);
  }
}
