import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';

/// Controller untuk form identification pada public recruitment
class PublicFormIdentificationController extends BaseControllers {
  // Form controllers
  final TextEditingController fullNameController = TextEditingController();
  final TextEditingController birthPlaceController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController rtController = TextEditingController();
  final TextEditingController rwController = TextEditingController();
  final TextEditingController kelurahanController = TextEditingController();
  final TextEditingController kecamatanController = TextEditingController();
  final TextEditingController kotaController = TextEditingController();
  final TextEditingController provinceController = TextEditingController();
  final TextEditingController postalCodeController = TextEditingController();
  
  // Reactive variables
  RxString selectedGender = ''.obs;
  RxString selectedReligion = ''.obs;
  RxString selectedMaritalStatus = ''.obs;
  RxString selectedEducation = ''.obs;
  Rx<DateTime?> birthDate = Rx<DateTime?>(null);
  RxBool isFormValid = false.obs;

  // Dummy data options
  final List<String> genderOptions = ['Laki-laki', 'Perempuan'];
  final List<String> religionOptions = [
    'Islam', 'Kristen', 'Katolik', 'Hindu', 'Buddha', 'Konghucu'
  ];
  final List<String> maritalStatusOptions = [
    'Belum Menikah', 'Menikah', 'Cerai Hidup', 'Cerai Mati'
  ];
  final List<String> educationOptions = [
    'SD', 'SMP', 'SMA/SMK', 'D1', 'D2', 'D3', 'S1', 'S2', 'S3'
  ];

  @override
  void onInit() {
    super.onInit();
    setupValidation();
  }

  void setupValidation() {
    // Listen to text changes for validation
    fullNameController.addListener(updateFormValidation);
    birthPlaceController.addListener(updateFormValidation);
    addressController.addListener(updateFormValidation);
    rtController.addListener(updateFormValidation);
    rwController.addListener(updateFormValidation);
    kelurahanController.addListener(updateFormValidation);
    kecamatanController.addListener(updateFormValidation);
    kotaController.addListener(updateFormValidation);
    provinceController.addListener(updateFormValidation);
    postalCodeController.addListener(updateFormValidation);
  }

  void updateFormValidation() {
    isFormValid.value = fullNameController.text.trim().isNotEmpty &&
        birthPlaceController.text.trim().isNotEmpty &&
        birthDate.value != null &&
        selectedGender.value.isNotEmpty &&
        selectedReligion.value.isNotEmpty &&
        selectedMaritalStatus.value.isNotEmpty &&
        selectedEducation.value.isNotEmpty &&
        addressController.text.trim().isNotEmpty &&
        rtController.text.trim().isNotEmpty &&
        rwController.text.trim().isNotEmpty &&
        kelurahanController.text.trim().isNotEmpty &&
        kecamatanController.text.trim().isNotEmpty &&
        kotaController.text.trim().isNotEmpty &&
        provinceController.text.trim().isNotEmpty &&
        postalCodeController.text.trim().isNotEmpty;
  }

  void setGender(String gender) {
    selectedGender.value = gender;
    updateFormValidation();
  }

  void setReligion(String religion) {
    selectedReligion.value = religion;
    updateFormValidation();
  }

  void setMaritalStatus(String status) {
    selectedMaritalStatus.value = status;
    updateFormValidation();
  }

  void setEducation(String education) {
    selectedEducation.value = education;
    updateFormValidation();
  }

  void setBirthDate(DateTime date) {
    birthDate.value = date;
    updateFormValidation();
  }

  bool validateForm() {
    updateFormValidation();
    return isFormValid.value;
  }

  Map<String, dynamic> getFormData() {
    return {
      'fullName': fullNameController.text.trim(),
      'birthPlace': birthPlaceController.text.trim(),
      'birthDate': birthDate.value?.toIso8601String(),
      'gender': selectedGender.value,
      'religion': selectedReligion.value,
      'maritalStatus': selectedMaritalStatus.value.replaceAll(' ', '_'),
      'education': selectedEducation.value,
      'address': addressController.text.trim(),
      'rt': rtController.text.trim(),
      'rw': rwController.text.trim(),
      'kelurahan': kelurahanController.text.trim(),
      'kecamatan': kecamatanController.text.trim(),
      'kota': kotaController.text.trim(),
      'province': provinceController.text.trim(),
      'postalCode': postalCodeController.text.trim(),
    };
  }

  void clearForm() {
    fullNameController.clear();
    birthPlaceController.clear();
    addressController.clear();
    rtController.clear();
    rwController.clear();
    kelurahanController.clear();
    kecamatanController.clear();
    kotaController.clear();
    provinceController.clear();
    postalCodeController.clear();
    
    selectedGender.value = '';
    selectedReligion.value = '';
    selectedMaritalStatus.value = '';
    selectedEducation.value = '';
    birthDate.value = null;
    isFormValid.value = false;
  }

  @override
  void onClose() {
    fullNameController.dispose();
    birthPlaceController.dispose();
    addressController.dispose();
    rtController.dispose();
    rwController.dispose();
    kelurahanController.dispose();
    kecamatanController.dispose();
    kotaController.dispose();
    provinceController.dispose();
    postalCodeController.dispose();
    super.onClose();
  }
}
