import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';

/// Controller untuk form verification pada public recruitment
class PublicFormVerificationController extends BaseControllers {
  // Form controllers
  final TextEditingController nikController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  
  // Reactive variables
  RxBool isNikValid = false.obs;
  RxBool isPhoneValid = false.obs;
  RxBool isEmailValid = false.obs;
  RxBool isFormValid = false.obs;

  @override
  void onInit() {
    super.onInit();
    setupValidation();
  }

  void setupValidation() {
    // Listen to text changes for validation
    nikController.addListener(() {
      validateNik();
      updateFormValidation();
    });
    
    phoneController.addListener(() {
      validatePhone();
      updateFormValidation();
    });
    
    emailController.addListener(() {
      validateEmail();
      updateFormValidation();
    });
  }

  void validateNik() {
    final nik = nikController.text.trim();
    // NIK validation: 16 digits
    isNikValid.value = nik.length == 16 && RegExp(r'^\d{16}$').hasMatch(nik);
  }

  void validatePhone() {
    final phone = phoneController.text.trim();
    // Phone validation: starts with 08 or +62, minimum 10 digits
    isPhoneValid.value = phone.length >= 10 && 
        (phone.startsWith('08') || phone.startsWith('+62'));
  }

  void validateEmail() {
    final email = emailController.text.trim();
    // Email validation
    isEmailValid.value = email.isNotEmpty && 
        RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  void updateFormValidation() {
    isFormValid.value = isNikValid.value && isPhoneValid.value && isEmailValid.value;
  }

  bool validateForm() {
    validateNik();
    validatePhone();
    validateEmail();
    updateFormValidation();
    return isFormValid.value;
  }

  Map<String, dynamic> getFormData() {
    return {
      'nik': nikController.text.trim(),
      'phone': phoneController.text.trim(),
      'email': emailController.text.trim(),
    };
  }

  void clearForm() {
    nikController.clear();
    phoneController.clear();
    emailController.clear();
    isNikValid.value = false;
    isPhoneValid.value = false;
    isEmailValid.value = false;
    isFormValid.value = false;
  }

  @override
  void onClose() {
    nikController.dispose();
    phoneController.dispose();
    emailController.dispose();
    super.onClose();
  }
}
