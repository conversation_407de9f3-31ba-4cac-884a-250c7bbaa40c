import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';

/// Controller untuk form self identification pada public recruitment
class PublicFormSelfIdentificationController extends BaseControllers {
  // Form controllers
  final TextEditingController emailVerifController = TextEditingController();
  final TextEditingController bankNameController = TextEditingController();
  final TextEditingController accountNumberController = TextEditingController();
  final TextEditingController accountNameController = TextEditingController();
  final TextEditingController emergencyContactNameController = TextEditingController();
  final TextEditingController emergencyContactPhoneController = TextEditingController();
  final TextEditingController emergencyContactRelationController = TextEditingController();
  
  // Reactive variables
  RxString emailVerif = ''.obs;
  RxBool isEmailVerified = false.obs;
  RxBool isCountdownActive = false.obs;
  RxString countdownDisplay = ''.obs;
  RxBool isFormValid = false.obs;
  
  // Email verification
  Timer? _countdownTimer;
  Timer? _verificationPollingTimer;
  int _countdownSeconds = 60;

  // Dummy data options
  final List<String> bankOptions = [
    'BCA', 'BNI', 'BRI', 'Mandiri', 'CIMB Niaga', 'Danamon', 'Permata', 'BTN'
  ];
  
  final List<String> relationOptions = [
    'Orang Tua', 'Saudara', 'Pasangan', 'Anak', 'Teman', 'Lainnya'
  ];

  @override
  void onInit() {
    super.onInit();
    setupValidation();
  }

  void setupValidation() {
    // Listen to text changes for validation
    emailVerifController.addListener(updateFormValidation);
    bankNameController.addListener(updateFormValidation);
    accountNumberController.addListener(updateFormValidation);
    accountNameController.addListener(updateFormValidation);
    emergencyContactNameController.addListener(updateFormValidation);
    emergencyContactPhoneController.addListener(updateFormValidation);
    emergencyContactRelationController.addListener(updateFormValidation);
  }

  void updateFormValidation() {
    isFormValid.value = emailVerifController.text.trim().isNotEmpty &&
        bankNameController.text.trim().isNotEmpty &&
        accountNumberController.text.trim().isNotEmpty &&
        accountNameController.text.trim().isNotEmpty &&
        emergencyContactNameController.text.trim().isNotEmpty &&
        emergencyContactPhoneController.text.trim().isNotEmpty &&
        emergencyContactRelationController.text.trim().isNotEmpty;
  }

  void setEmailVerif(String email) {
    emailVerif.value = email;
    emailVerifController.text = email;
    updateFormValidation();
  }

  void setBankName(String bank) {
    bankNameController.text = bank;
    updateFormValidation();
  }

  void setEmergencyContactRelation(String relation) {
    emergencyContactRelationController.text = relation;
    updateFormValidation();
  }

  // Email verification methods
  void startEmailVerificationPolling() {
    // Simulate email verification polling
    _verificationPollingTimer = Timer.periodic(Duration(seconds: 5), (timer) {
      // Dummy verification check - in real app, this would call API
      // For demo, auto-verify after 30 seconds
      if (timer.tick >= 6) {
        isEmailVerified.value = true;
        timer.cancel();
        
        // Auto navigate to next page after verification
        Future.delayed(Duration(seconds: 2), () {
          goToPageThree();
        });
      }
    });
  }

  void goToPageThree() {
    // This will be called from parent controller
    isEmailVerified.value = true;
    stopVerificationPolling();
  }

  void stopVerificationPolling() {
    _verificationPollingTimer?.cancel();
    _verificationPollingTimer = null;
  }

  void restartCountdown() {
    _countdownSeconds = 60;
    isCountdownActive.value = true;
    
    _countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (_countdownSeconds > 0) {
        _countdownSeconds--;
        int minutes = _countdownSeconds ~/ 60;
        int seconds = _countdownSeconds % 60;
        countdownDisplay.value = '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
      } else {
        isCountdownActive.value = false;
        timer.cancel();
      }
    });
  }

  bool validateForm() {
    updateFormValidation();
    return isFormValid.value;
  }

  Map<String, dynamic> getFormData() {
    return {
      'emailVerif': emailVerif.value,
      'bankName': bankNameController.text.trim(),
      'accountNumber': accountNumberController.text.trim(),
      'accountName': accountNameController.text.trim(),
      'emergencyContactName': emergencyContactNameController.text.trim(),
      'emergencyContactPhone': emergencyContactPhoneController.text.trim(),
      'emergencyContactRelation': emergencyContactRelationController.text.trim(),
      'isEmailVerified': isEmailVerified.value,
    };
  }

  void clearForm() {
    emailVerifController.clear();
    bankNameController.clear();
    accountNumberController.clear();
    accountNameController.clear();
    emergencyContactNameController.clear();
    emergencyContactPhoneController.clear();
    emergencyContactRelationController.clear();
    
    emailVerif.value = '';
    isEmailVerified.value = false;
    isCountdownActive.value = false;
    countdownDisplay.value = '';
    isFormValid.value = false;
    
    stopVerificationPolling();
    _countdownTimer?.cancel();
  }

  @override
  void onClose() {
    emailVerifController.dispose();
    bankNameController.dispose();
    accountNumberController.dispose();
    accountNameController.dispose();
    emergencyContactNameController.dispose();
    emergencyContactPhoneController.dispose();
    emergencyContactRelationController.dispose();
    
    _countdownTimer?.cancel();
    _verificationPollingTimer?.cancel();
    super.onClose();
  }
}
