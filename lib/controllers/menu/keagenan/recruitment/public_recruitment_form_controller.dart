import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_identification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_self_identification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_verification_controller.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:pdl_superapp/utils/public_api.dart';
import 'package:pdl_superapp/utils/utils.dart';

/// Controller khusus untuk public recruitment form
/// Menggunakan PublicApi instead of regular Api
class PublicRecruitmentFormController extends BaseControllers {
  // Public API instance
  final PublicApi publicApi = Get.put(PublicApi());

  // Page management
  final PageController pageController = PageController();
  RxInt activePage = 0.obs;
  RxBool isReady = false.obs;
  RxBool isVerificationEmailSent = false.obs;

  // Form controllers - reuse existing ones
  late FormVerificationController verificationController;
  late FormIdentificationController identificationController;
  late FormSelfIdentificationController selfIdentificationController;

  @override
  void onInit() {
    super.onInit();
    initializeControllers();
  }

  void initializeControllers() {
    verificationController = Get.put(
      FormVerificationController(),
      tag: 'public_verification',
    );
    identificationController = Get.put(
      FormIdentificationController(),
      tag: 'public_identification',
    );
    selfIdentificationController = Get.put(
      FormSelfIdentificationController(),
      tag: 'public_self_identification',
    );

    // Set ready state
    isReady.value = true;
  }

  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    setLoading(false);
    
    switch (requestCode) {
      case kReqSubmitRecruitmentForm:
        _handleSubmissionSuccess(response);
        break;
      case kReqSendVerificationEmail:
        _handleVerificationEmailSent();
        break;
      default:
    }
  }

  void _handleSubmissionSuccess(dynamic response) {
    Utils.popup(
      body: 'Formulir rekrutmen berhasil dikirim!',
      type: 'success',
    );
    
    // Navigate back or to success page
    Get.back();
  }

  void _handleVerificationEmailSent() {
    isVerificationEmailSent.value = true;
    selfIdentificationController.startEmailVerificationPolling();
  }

  /// Validate current page
  bool validateCurrentPage() {
    switch (activePage.value) {
      case 0:
        return verificationController.validateForm();
      case 1:
        return identificationController.validateForm();
      case 2:
        return selfIdentificationController.validateForm();
      case 3:
        return true; // Terms page validation
      default:
        return false;
    }
  }

  /// Submit form for email verification
  void submitFormForVerification() {
    if (!validateCurrentPage()) {
      Utils.popup(
        body: 'Mohon lengkapi semua field yang wajib diisi',
        type: 'failed',
      );
      return;
    }

    // Send verification email
    _sendVerificationEmail();
  }

  void _sendVerificationEmail() {
    // Implementation for sending verification email via public API
    // This would typically call a public endpoint for email verification
    isVerificationEmailSent.value = true;
    selfIdentificationController.startEmailVerificationPolling();
  }

  /// Submit final form
  Future<void> submitFormFinal() async {
    if (!validateCurrentPage()) {
      Utils.popup(
        body: 'Mohon lengkapi semua field yang wajib diisi',
        type: 'failed',
      );
      return;
    }

    setLoading(true);
    
    final formData = _collectFormData();
    
    await publicApi.submitPublicRecruitmentForm(
      controllers: this,
      data: formData,
      code: kReqSubmitRecruitmentForm,
    );
  }

  /// Collect all form data
  Map<String, dynamic> _collectFormData() {
    return {
      'verification': verificationController.getFormData(),
      'identification': identificationController.getFormData(),
      'selfIdentification': selfIdentificationController.getFormData(),
      'submittedAt': DateTime.now().toIso8601String(),
      'isPublicSubmission': true,
    };
  }

  /// Save form data locally (for public forms, we might save to local storage)
  Future<bool> saveFormData() async {
    try {
      final formData = _collectFormData();
      
      // For public forms, we could save to SharedPreferences or local database
      // This is a simplified implementation
      Utils.popup(
        body: 'Form berhasil disimpan secara lokal',
        type: 'success',
      );
      
      return true;
    } catch (e) {
      Utils.popup(
        body: 'Gagal menyimpan form: $e',
        type: 'failed',
      );
      return false;
    }
  }

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }
}
