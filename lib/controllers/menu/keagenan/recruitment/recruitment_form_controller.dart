import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_verification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_identification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_self_identification_controller.dart';
import 'package:pdl_superapp/controllers/menu/keagenan/recruitment/form_terms_controller.dart';
import 'package:pdl_superapp/models/recruitment_form_model.dart';
import 'package:pdl_superapp/routes/app_routes.dart';
import 'package:pdl_superapp/utils/form_firestore_service.dart';
import 'package:pdl_superapp/utils/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

class RecruitmentFormController extends BaseControllers {
  PageController pageController = PageController();
  RxInt activePage = 0.obs;

  RxBool isVerificationEmailSent = false.obs;

  late SharedPreferences prefs;

  // Form ID untuk Firestore
  RxString formId = ''.obs;
  bool hasServerUuid = false;

  // Status form
  RxBool isFormLoaded = false.obs;
  RxBool isFormSaving = false.obs;
  RxBool isFormSubmitted = false.obs;
  RxString formStatus = 'draft'.obs;

  // Timer untuk auto-save
  Timer? _autoSaveTimer;
  final int _autoSaveIntervalSeconds = 5; // Auto-save setiap 5 detik

  // Service untuk Firestore
  final FormFirestoreService _firestoreService = FormFirestoreService();

  // Sub-controllers
  late FormVerificationController verificationController;
  late FormIdentificationController identificationController;
  late FormSelfIdentificationController selfIdentificationController;
  late FormTermsController termsController;

  RxBool isReady = false.obs;

  @override
  void onInit() async {
    super.onInit();
    prefs = await SharedPreferences.getInstance();

    // Initialize sub-controllers
    verificationController = Get.put(
      FormVerificationController(baseController: this),
    );
    identificationController = Get.put(
      FormIdentificationController(baseController: this),
    );
    selfIdentificationController = Get.put(
      FormSelfIdentificationController(baseController: this),
    );
    termsController = Get.put(FormTermsController(baseController: this));

    // Inisialisasi form ID atau ambil dari parameter jika ada
    _initFormId();
    // Setup listener untuk perubahan form
    _setupFormChangeListeners();
  }

  @override
  void onReady() {
    super.onReady();
    isReady.value = true;
  }

  // Delegate to sub-controllers for API responses
  @override
  void loadSuccess({
    required int requestCode,
    required response,
    required int statusCode,
  }) {
    super.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );

    // Handle submit recruitment form response
    if (requestCode == kReqSubmitRecruitmentForm) {
      _handleSubmitFormSuccess(response);
      return;
    }

    // Delegate to appropriate sub-controller
    verificationController.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    selfIdentificationController.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
    termsController.loadSuccess(
      requestCode: requestCode,
      response: response,
      statusCode: statusCode,
    );
  }

  // Handle submit form success response
  void _handleSubmitFormSuccess(response) async {
    log('Submit form success response: $response');

    // Update form status to submitted
    isFormSubmitted.value = true;
    formStatus.value = 'submitted';

    // Delete form from Firestore (both local and online) since it's successfully submitted
    try {
      log(
        'Deleting form from Firestore after successful submission: ${formId.value}',
      );
      final deleteResult = await _firestoreService.deleteRecruitmentForm(
        formId.value,
      );
      if (deleteResult) {
        log('Successfully deleted form from Firestore: ${formId.value}');
      } else {
        log('Failed to delete form from Firestore: ${formId.value}');
      }
    } catch (e) {
      log('Error deleting form from Firestore: $e');
    }

    // Show success message
    Get.snackbar(
      'Berhasil',
      'Form berhasil disubmit dan telah dihapus dari penyimpanan lokal',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Get.theme.colorScheme.primary,
      colorText: Get.theme.colorScheme.onPrimary,
    );

    // Navigate back to recruitment list
    Get.offNamedUntil(Routes.KEAGENAN_LIST, (route) => false);
  }

  // Load recruiter data from SharedPreferences
  void loadRecruiterDataFromPrefs() {
    verificationController.loadRecruiterDataFromPrefs();
  }

  // Inisialisasi form ID
  Future<void> _initFormId() async {
    // Cek apakah ada form ID dari parameter
    if (Get.parameters.containsKey('formId')) {
      formId.value = Get.parameters['formId']!;
      log('Menggunakan form ID dari parameter: ${formId.value}');
      // Load form data jika ada
      _loadFormData();
    } else {
      // Buat form ID baru menggunakan UUID
      formId.value = const Uuid().v4();
      log('Membuat form ID baru: ${formId.value}');
      loadRecruiterDataFromPrefs();
    }
  }

  // Setup listener untuk perubahan form
  void _setupFormChangeListeners() {
    // Setup listeners for sub-controllers to notify main controller
    // This will be handled by each sub-controller calling onFormChanged()
  }

  // Handler ketika form berubah - called by sub-controllers
  void onFormChanged() {
    // Jika timer sudah berjalan, reset
    _autoSaveTimer?.cancel();

    // Mulai timer baru untuk auto-save
    _autoSaveTimer = Timer(Duration(seconds: _autoSaveIntervalSeconds), () {
      // Simpan form data
      saveFormData();
    });
  }

  // Load form data dari Firestore
  Future<void> _loadFormData() async {
    if (formId.value.isEmpty) {
      log('Form ID kosong, tidak dapat memuat data form');
      return;
    }

    setLoading(true);

    try {
      final formData = await _firestoreService.getRecruitmentForm(formId.value);

      if (formData != null) {
        // Isi form dengan data yang ada
        _populateFormWithData(formData);
        isFormLoaded.value = true;
        log('Berhasil memuat data form dengan ID: ${formId.value}');
      } else {
        log('Tidak ada data form dengan ID: ${formId.value}');
      }
    } catch (e) {
      log('Error saat memuat data form: $e');
    } finally {
      setLoading(false);
    }
  }

  // Delegate form population to sub-controllers
  void _populateFormWithData(RecruitmentFormModel formData) {
    // Delegate to sub-controllers
    verificationController.populateFormData(formData);
    identificationController.populateFormData(formData);
    selfIdentificationController.populateFormData(formData);
    termsController.populateFormData(formData);

    // Set status form
    isFormSubmitted.value = formData.isSubmitted ?? false;
    formStatus.value = formData.formStatus ?? 'draft';

    hasServerUuid = formData.hasServerUuid ?? false;

    loadRecruiterDataFromPrefs();
  }

  // Simpan form data ke Firestore
  Future<bool> saveFormData({bool isSubmit = false}) async {
    if (formId.value.isEmpty) {
      log('Form ID kosong, tidak dapat menyimpan data form');
      return false;
    }

    isFormSaving.value = true;

    try {
      // Buat model dari data form saat ini
      final formData = _createFormModel(isSubmit: isSubmit);

      // Simpan ke Firestore
      final result = await _firestoreService.saveRecruitmentForm(
        formData,
        formId.value,
      );

      if (result) {
        log('Berhasil menyimpan data form dengan ID: ${formId.value}');
        if (isSubmit) {
          isFormSubmitted.value = true;
          formStatus.value = 'submitted';
        }
      } else {
        log('Gagal menyimpan data form dengan ID: ${formId.value}');
      }

      return result;
    } catch (e) {
      log('Error saat menyimpan data form: $e');
      return false;
    } finally {
      isFormSaving.value = false;
    }
  }

  // Buat model dari data form saat ini - delegate to sub-controllers
  RecruitmentFormModel _createFormModel({bool isSubmit = false}) {
    return RecruitmentFormModel(
      id: formId.value,

      // Get data from sub-controllers
      recruiterName: verificationController.recruiterName.value,
      recruiterId: verificationController.recruiterId.value,
      recruiterBranch: verificationController.recruiterBranch.value,
      recruiterCode: verificationController.recruiterCode.value,
      recruiterLevel: verificationController.recruiterLevel.value,
      recruiterPhoto: verificationController.recruiterPhoto.value,
      candidateLevel: verificationController.candidateLevelController.text,
      candidateBranch: verificationController.candidateBranchController.text,
      candidateBranchCode: verificationController.candidateBranchCode.value,

      // Data FormIdentification
      nik: identificationController.nikController.text,
      namaKtp: identificationController.namaKtpController.text,
      tempatLahir: identificationController.tempatLahirController.text,
      tanggalLahir: identificationController.tanggalLahirController.text,
      bulanLahir: identificationController.bulanLahirController.text,
      tahunLahir: identificationController.tahunLahirController.text,
      jenisKelamin: identificationController.jenisKelaminController.text,
      alamatKtp: identificationController.alamatKtpController.text,
      rtKtp: identificationController.rtKtpController.text,
      rwKtp: identificationController.rwKtpController.text,
      provinsiKtp: identificationController.provinsiKtpController.text,
      kabupatenKtp: identificationController.kabupatenKtpController.text,
      kecamatanKtp: identificationController.kecamatanKtpController.text,
      kelurahanKtp: identificationController.kelurahanKtpController.text,
      maritalStatus: identificationController.selectedMaritalStatus.value
          .replaceAll(' ', '_'),

      // Data Alamat Domisili
      alamatDomisili: identificationController.alamatDomisiliController.text,
      rtDomisili: identificationController.rtDomisiliController.text,
      rwDomisili: identificationController.rwDomisiliController.text,
      provinsiDomisili:
          identificationController.provinsiDomisiliController.text,
      kabupatenDomisili:
          identificationController.kabupatenDomisiliController.text,
      kecamatanDomisili:
          identificationController.kecamatanDomisiliController.text,
      kelurahanDomisili:
          identificationController.kelurahanDomisiliController.text,

      // Data FormSelfIdentification
      email: selfIdentificationController.emailController.text,
      nomorHp: selfIdentificationController.nomorHpController.text,
      occupation: selfIdentificationController.pekerjaanController.text,
      occupationCode: selfIdentificationController.pekerjaanCodeController.text,
      emergencyNama: selfIdentificationController.emergencyNamaController.text,
      emergencyHubungan:
          selfIdentificationController.emergencyHubunganController.text,
      emergencyNomorHp:
          selfIdentificationController.emergencyNomorHpController.text,
      namaPemilikRekening:
          selfIdentificationController.namaPemilikRekeningController.text,
      nomorRekening: selfIdentificationController.nomorRekeningController.text,
      namaBank: selfIdentificationController.namaBankController.text,
      bankCode: selfIdentificationController.bankCode.value,

      // Data Foto
      ktpImagePath: verificationController.ktpImage.value?.path,
      selfieKtpImagePath: verificationController.selfieKtpImage.value?.path,
      pasFotoImagePath: verificationController.pasFotoImage.value?.path,

      // Data URL Foto
      ktpImageUrl: verificationController.ktpUrl.value,
      selfieKtpImageUrl: verificationController.selfieKtpUrl.value,
      pasFotoImageUrl: verificationController.pasFotoUrl.value,

      // Terms and Signature
      signature: termsController.signatureData.value,
      paraf: termsController.parafData.value,

      // Signature and Paraf URLs
      signatureUrl: termsController.signatureUrl.value,
      parafUrl: termsController.parafUrl.value,

      // Last job field
      lastJob: selfIdentificationController.lastJob.value,

      // New structured data fields
      last5YearJobData:
          selfIdentificationController.last5YearJobData
              .map((item) => item.toJson())
              .toList(),
      last2YearProductionData:
          selfIdentificationController.last2YearProductionData
              .map((item) => item.toJson())
              .toList(),
      lastCompanyManPowerData:
          selfIdentificationController.lastCompanyManPowerData.value.toJson(),
      rewardInfoData:
          selfIdentificationController.rewardInfoData
              .map((item) => item.toJson())
              .toList(),

      // Metadata
      lastUpdated: DateTime.now().millisecondsSinceEpoch,
      isSubmitted: isSubmit ? true : isFormSubmitted.value,
      formStatus: isSubmit ? 'submitted' : formStatus.value,
      hasServerUuid:
          false, // Default false, akan diupdate setelah mendapat UUID dari server
    );
  }

  Future<bool> submitForm() async {
    return await saveFormData(isSubmit: true);
  }

  // Update form ID setelah mendapat UUID dari server
  Future<bool> updateFormId(String newFormId) async {
    try {
      if (formId.value.isEmpty || newFormId.isEmpty) {
        log('Cannot update form ID: old or new ID is empty');
        return false;
      }

      if (formId.value == newFormId) {
        log('Form ID is already the same, no update needed');
        return true;
      }

      String oldFormId = formId.value;
      log('Updating form ID from $oldFormId to $newFormId');

      // Pindahkan data form dari ID lama ke ID baru di Firestore
      final moveResult = await _firestoreService.moveRecruitmentForm(
        oldFormId,
        newFormId,
      );

      if (moveResult) {
        // Update form ID di controller
        formId.value = newFormId;
        log('Successfully updated form ID to: $newFormId');
        return true;
      } else {
        log('Failed to move form data from $oldFormId to $newFormId');
        return false;
      }
    } catch (e) {
      log('Error updating form ID: $e');
      return false;
    }
  }

  // Submit form for final submission
  Future<bool> submitFormFinal() async {
    var data = {
      "recruiterCode": verificationController.recruiterCode.value,
      "ktpPhoto": verificationController.ktpUrl.value,
      "selfiePhoto": verificationController.selfieKtpUrl.value,
      "passPhoto": verificationController.pasFotoUrl.value,
      "positionLevel": verificationController.recruiterLevel.value,
      "branch": verificationController.candidateBranchCode.value,
      "bank": selfIdentificationController.bankCode.value,
      "nik": identificationController.nikController.text,
      "fullName": identificationController.namaKtpController.text,
      "birthPlace": identificationController.tempatLahirController.text,
      "birthDate":
          "${identificationController.tahunLahirController.text}-${((identificationController.monthList.indexOf(identificationController.bulanLahirController.text) + 1)).toString().padLeft(2, '0')}-${identificationController.tanggalLahirController.text.padLeft(2, '0')}",
      "gender":
          identificationController.selectedJenisKelamin.value == 0 ? 'M' : 'F',
      "ktpProvince": identificationController.provinsiKtpController.text,
      "ktpCity": identificationController.kabupatenKtpController.text,
      "ktpDistrict": identificationController.kecamatanKtpController.text,
      "ktpSubDistrict": identificationController.kelurahanKtpController.text,
      "ktpRt": identificationController.rtKtpController.text,
      "ktpRw": identificationController.rwKtpController.text,
      "ktpAddress": identificationController.alamatKtpController.text,
      "isDomicileSameAsKtp":
          identificationController.selectedIsAddressSame.value == 0
              ? true
              : false,
      "domicileProvince":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.provinsiKtpController.text
              : identificationController.provinsiDomisiliController.text,
      "domicileCity":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kabupatenKtpController.text
              : identificationController.kabupatenDomisiliController.text,
      "domicileDistrict":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kecamatanKtpController.text
              : identificationController.kecamatanDomisiliController.text,
      "domicileSubDistrict":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kelurahanKtpController.text
              : identificationController.kelurahanDomisiliController.text,
      "domicileRt":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.rtKtpController.text
              : identificationController.rtDomisiliController.text,
      "domicileRw":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.rwKtpController.text
              : identificationController.rwDomisiliController.text,
      "domicileAddress":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.alamatKtpController.text
              : identificationController.alamatDomisiliController.text,
      "phoneNumber": selfIdentificationController.nomorHpController.text,
      "maritalStatus": identificationController.selectedMaritalStatus.value
          .replaceAll(' ', '_'),
      "occupation": selfIdentificationController.pekerjaanController.text,
      "occupationCode":
          selfIdentificationController.pekerjaanCodeController.text,
      "email": selfIdentificationController.emailController.text,
      "emergencyContactName":
          selfIdentificationController.emergencyNamaController.text,
      "emergencyContactRelation":
          selfIdentificationController.emergencyHubunganController.text,
      "emergencyContactPhone":
          selfIdentificationController.emergencyNomorHpController.text,
      "bankAccountName":
          selfIdentificationController.namaPemilikRekeningController.text,
      "bankAccountNumber":
          selfIdentificationController.nomorRekeningController.text,
      "lastJob": selfIdentificationController.lastJob.value,
      "signature": termsController.signatureUrl.value,
      "paraf": termsController.parafUrl.value,
      "last5YearJobData": [
        for (
          int i = 0;
          i < selfIdentificationController.last5YearJobData.length;
          i++
        )
          {
            "year": selfIdentificationController.last5YearJobData[i].year,
            "company": selfIdentificationController.last5YearJobData[i].company,
            "position":
                selfIdentificationController.last5YearJobData[i].position,
          },
      ],
      // selfIdentificationController.last5YearJobData
      //     .map((item) => item.toJson())
      //     .toList(),
      "last2YearProductionData":
          selfIdentificationController.last2YearProductionData
              .map((item) => item.toJson())
              .toList(),
      "lastCompanyManPowerData":
          selfIdentificationController.lastCompanyManPowerData.value.toJson(),
      "rewardInfoData":
          selfIdentificationController.rewardInfoData
              .map((item) => item.toJson())
              .toList(),
    };
    log('Final submit data: $data');

    // Submit form to API
    api.performSubmitRecruitmentForm(
      controllers: this,
      data: data,
      code: kReqSubmitRecruitmentForm,
    );
    return true;
  }

  // Submit form
  Future<bool> submitFormForVerification() async {
    var data = {
      "recruiterCode": verificationController.recruiterCode.value,
      "ktpPhoto": verificationController.ktpUrl.value,
      "selfiePhoto": verificationController.selfieKtpUrl.value,
      "passPhoto": verificationController.pasFotoUrl.value,
      "positionLevel": verificationController.recruiterLevel.value,
      "branch": verificationController.candidateBranchCode.value,
      "bank": selfIdentificationController.bankCode.value,
      "nik": identificationController.nikController.text,
      "fullName": identificationController.namaKtpController.text,
      "birthPlace": identificationController.tempatLahirController.text,
      "birthDate":
          "${identificationController.tahunLahirController.text}-${((identificationController.monthList.indexOf(identificationController.bulanLahirController.text) + 1)).toString().padLeft(2, '0')}-${identificationController.tanggalLahirController.text.padLeft(2, '0')}",
      "gender":
          identificationController.selectedJenisKelamin.value == 0 ? 'M' : 'F',
      "ktpProvince": identificationController.provinsiKtpController.text,
      "ktpCity": identificationController.kabupatenKtpController.text,
      "ktpDistrict": identificationController.kecamatanKtpController.text,
      "ktpSubDistrict": identificationController.kelurahanKtpController.text,
      "ktpRt": identificationController.rtKtpController.text,
      "ktpRw": identificationController.rwKtpController.text,
      "ktpAddress": identificationController.alamatKtpController.text,
      "isDomicileSameAsKtp":
          identificationController.selectedIsAddressSame.value == 0
              ? true
              : false,
      "domicileProvince":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.provinsiKtpController.text
              : identificationController.provinsiDomisiliController.text,
      "domicileCity":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kabupatenKtpController.text
              : identificationController.kabupatenDomisiliController.text,
      "domicileDistrict":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kecamatanKtpController.text
              : identificationController.kecamatanDomisiliController.text,
      "domicileSubDistrict":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.kelurahanKtpController.text
              : identificationController.kelurahanDomisiliController.text,
      "domicileRt":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.rtKtpController.text
              : identificationController.rtDomisiliController.text,
      "domicileRw":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.rwKtpController.text
              : identificationController.rwDomisiliController.text,
      "domicileAddress":
          identificationController.selectedIsAddressSame.value == 0
              ? identificationController.alamatKtpController.text
              : identificationController.alamatDomisiliController.text,
      "phoneNumber": selfIdentificationController.nomorHpController.text,
      "maritalStatus": identificationController.selectedMaritalStatus.value
          .replaceAll(' ', '_'),
      "occupation": selfIdentificationController.pekerjaanController.text,
      "occupationCode":
          selfIdentificationController.pekerjaanCodeController.text,
      "email": selfIdentificationController.emailController.text,
      "emergencyContactName":
          selfIdentificationController.emergencyNamaController.text,
      "emergencyContactRelation":
          selfIdentificationController.emergencyHubunganController.text,
      "emergencyContactPhone":
          selfIdentificationController.emergencyNomorHpController.text,
      "bankAccountName":
          selfIdentificationController.namaPemilikRekeningController.text,
      "bankAccountNumber":
          selfIdentificationController.nomorRekeningController.text,
      "lastJob": selfIdentificationController.lastJob.value,
      "signature": termsController.signatureUrl.value,
      "paraf": termsController.parafUrl.value,
      "last5YearJobData": [
        for (
          int i = 0;
          i < selfIdentificationController.last5YearJobData.length;
          i++
        )
          {
            "year": selfIdentificationController.last5YearJobData[i].year,
            "company": selfIdentificationController.last5YearJobData[i].company,
            "position":
                selfIdentificationController.last5YearJobData[i].position,
          },
      ],
      // selfIdentificationController.last5YearJobData
      //     .map((item) => item.toJson())
      //     .toList(),
      "last2YearProductionData":
          selfIdentificationController.last2YearProductionData
              .map((item) => item.toJson())
              .toList(),
      "lastCompanyManPowerData":
          selfIdentificationController.lastCompanyManPowerData.value.toJson(),
      "rewardInfoData":
          selfIdentificationController.rewardInfoData
              .map((item) => item.toJson())
              .toList(),
    };

    log('data: $data');
    // 1. send data as draft
    // kalau data sudah di server (hasServerUuid == true) tidak perlu di send as draft lagi
    if (hasServerUuid) {
      // send Verification
      selfIdentificationController.sendVerification(formId.value);
    } else {
      api.performSaveDraft(controllers: this, data: data, code: kReqSaveDraft);
    }
    return true;
  }

  // Delegate image picking to verification controller
  Future<void> pickKtpImage(String title, String type) async {
    await verificationController.pickKtpImage(title, type);
  }

  // Delegate image clearing to verification controller
  void clearImage(String type) {
    verificationController.clearImage(type);
  }

  // Delegate KTP OCR processing to identification controller
  Future<void> processKtpOcr(file) async {
    await identificationController.processKtpOcr(file);
  }

  // Delegate branch text change to verification controller
  void onBranchTextChanged(String value) {
    verificationController.onBranchTextChanged(value);
  }

  // Validate current page before navigation
  bool validateCurrentPage() {
    switch (activePage.value) {
      case 0:
        return verificationController.validateForm();
      case 1:
        return identificationController.validateForm();
      case 2:
        return selfIdentificationController.validateForm();
      case 3:
        return termsController.validateForm();
      default:
        return true;
    }
  }

  // Navigation methods
  void nextPage() {
    if (activePage.value < 3) {
      activePage.value++;
      pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void previousPage() {
    if (activePage.value > 0) {
      activePage.value--;
      pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void goToPage(int page) {
    if (page >= 0 && page <= 3) {
      activePage.value = page;
      pageController.animateToPage(
        page,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void onClose() {
    // Batalkan timer auto-save
    _autoSaveTimer?.cancel();

    // Dispose page controller
    pageController.dispose();

    // Dispose sub-controllers
    Get.delete<FormVerificationController>();
    Get.delete<FormIdentificationController>();
    Get.delete<FormSelfIdentificationController>();
    Get.delete<FormTermsController>();

    super.onClose();
  }
}
