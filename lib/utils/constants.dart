// Constants untuk padding sizes
import 'package:flutter/material.dart';

const double paddingExtraSmall = 4.0;
const double paddingSmall = 8.0;
const double paddingMedium = 16.0;
const double paddingLarge = 24.0;
const double paddingExtraLarge = 32.0;
const double radiusSmall = 8.0;
const double radiusMedium = 16.0;

// Colors
const kColorError = Color(0xFFFF4233);
// const kColorPaninBlue = Color(0xFF0C9DEB);
const kColorTextTersier = Color(0xFFB0B0B0);
const kColorGlobalGreen = Color(0xFF126F3C);
const kColorGlobalBgGreen = Color(0xFFF3FAF1);
const kColorGlobalWarning = Color(0xFF925016);
const kColorGlobalBgWarning = Color(0xFFFCF8EA);
const kColorGlobalBgRed = Color(0xFFFFF0F2);
const kColorErrorText = Color(0xFF93000A);
const kColorPaninBlue = Color(0xFF0162A3);
const kColorGlobalBlue = Color(0xFF0C9DEB);
const kColorGlobalBlue100 = Color(0xFFE0F1FE);
const kColorGlobalBgBlue = Color(0xFFF0F8FF);
const kColorGlobalRed = Color(0xFFE0001A);
const kLine = Color(0xFFF6F6F6);

const kColoTuatara400 = Color(0xFF888888);

const Color chipColorSuccess = Color(0xFF126F3C);
const Color chipColorDanger = Color.fromARGB(255, 123, 13, 37);
const Color chipColorWarning = Color(0xFF925016);
const Color chipColorInfo = Color(0xFF0162A3);

// Light
const kColorBgLight = Color(0xFFFFFFFF);
const kColorTextLight = Color(0xFF262626);
const kColorBorderLight = Color(0xFFE7E7E7);
const kColorTextTersierLight = Color(0xFF5D5D5D);
// Dark
const kColorBgDark = Color(0xFF283149);
const kColorTextDark = Color(0xFFD1D1D1);
const kColorBorderDark = Color(0xFF4F4F4F);

// Gradients
const kGradientBlue = LinearGradient(
  colors: [Color(0xFF2196F3), Color(0xFF90CAF9)],
  begin: Alignment.bottomCenter,
  end: Alignment.topCenter,
);

const kGradientGreen = LinearGradient(
  colors: [Color(0xFF4CB8C4), Color(0xFF3CD3AD)],
  begin: Alignment.bottomCenter,
  end: Alignment.topCenter,
);

const kGradientOrange = LinearGradient(
  colors: [Color(0xFFF46B45), Color(0xFFEEA849)],
  begin: Alignment.bottomCenter,
  end: Alignment.topCenter,
);

// static images
const kFlagId = 'assets/flag-id.svg';
const kFlagUs = 'assets/flag-us.svg';
const kFlagIdSquare = 'assets/ic-flag-id-square.svg';
const kFlagUsSquare = 'assets/ic-flag-us-square.svg';
const kIconImage = 'assets/img-text-logo-white.png';
const double ktpAspectRatio = 85.6 / 53.98;

// Popup types
const kPopupSuccess = 'success';
const kPopupFailed = 'failed';
const kPopupWarning = 'warning';
const kPopupInfo = 'info';

// LastJobTypes =
const kLastJobNotSales = 'Bukan Sales';
const kLastJobSalesIssurance = 'Sales Asuransi Jiwa';
const kLastJobSalesNotIssurance = 'Sales Tetapi Bukan Asuransi Jiwa';
