extension StringExt on String {
  String toCapitalize() {
    return split(' ')
        .map(
          (word) =>
              word.isNotEmpty
                  ? '${word[0].toUpperCase()}${word.substring(1).toLowerCase()}'
                  : '',
        )
        .join(' ');
  }

  bool inList(List<String> values) {
    for (var value in values) {
      if (value.toLowerCase() == toLowerCase()) {
        return true;
      }
    }
    return false;
  }
}
