import 'dart:developer';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'package:get/get_connect/http/src/request/request.dart';
import 'package:pdl_superapp/base/base_controllers.dart';
import 'package:pdl_superapp/utils/config_reader.dart';
import 'package:pdl_superapp/utils/logger_service.dart';
import 'package:pdl_superapp/utils/network_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// API Service khusus untuk endpoint public yang tidak memerlukan authentication
/// Tidak mengirimkan Authorization header
String baseUrl = ConfigReader.getPublicUrl();

class PublicApi extends GetConnect {
  @override
  void onInit() async {
    httpClient.baseUrl = baseUrl;
    httpClient.timeout = const Duration(seconds: 15);
    setupRequestModifier();
  }

  setupRequestModifier() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    httpClient.addRequestModifier((Request request) {
      String deviceId = prefs.getString('device_id') ?? '';

      // Tidak menambahkan Authorization header untuk public API
      request.headers['Content-Type'] = 'application/json';
      request.headers['Accept'] = 'application/json';
      request.headers['device_id'] = deviceId;

      _print(
        doPrint: false,
        data: 'Public API Request Headers: ${request.headers}',
      );
      return request;
    });
  }

  void _print({required bool doPrint, required dynamic data}) {
    if (doPrint) {
      log(data.toString());
    }
  }

  /// Check internet connection
  Future<bool> _checkInternetConnection() async {
    try {
      final networkManager = Get.find<NetworkManager>();
      if (!networkManager.isOnline) return false;
      return await networkManager.checkConnectionQuality();
    } catch (e) {
      try {
        Get.find<LoggerService>().log('Error finding NetworkManager: $e');
      } catch (_) {
        log('Error finding NetworkManager: $e');
      }
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    }
  }

  /// Public API GET request
  Future<void> publicApiFetch({
    required String url,
    required BaseControllers controller,
    int code = 0,
    bool debug = false,
  }) async {
    setupRequestModifier();
    try {
      final hasConnection = await _checkInternetConnection();

      if (!hasConnection) {
        Get.find<LoggerService>().log("tidak ada koneksi");
        return controller.loadError(
          "No internet connection. Please check your connection and try again.",
        );
      }

      _print(doPrint: debug, data: 'Public API URL: $url');

      final response = await get(url);

      _print(
        doPrint: debug,
        data: 'Public API Response: ${response.body ?? {}}',
      );

      if (response.status.hasError) {
        _print(doPrint: debug, data: 'Public API Error: $url');
        return controller.loadFailed(requestCode: code, response: response);
      }

      return controller.loadSuccess(
        requestCode: code,
        response: response.body ?? {},
        statusCode: response.status.code ?? 0,
      );
    } catch (e) {
      Get.find<LoggerService>().log('Public API Error: $e');
      return controller.loadError(e);
    }
  }

  /// Public API POST request
  Future<void> publicApiPost({
    required String url,
    required BaseControllers controller,
    required var data,
    int code = 1,
    bool debug = false,
  }) async {
    setupRequestModifier();
    try {
      final hasConnection = await _checkInternetConnection();

      if (!hasConnection) {
        Get.find<LoggerService>().log("tidak ada koneksi");
        return controller.loadError(
          "No internet connection. Cannot perform operation offline.",
        );
      }

      _print(doPrint: debug, data: 'Public API POST URL: $url');
      _print(doPrint: debug, data: 'Public API POST Data: $data');

      final response = await post(url, data);

      _print(
        doPrint: debug,
        data: 'Public API POST Response: ${response.body ?? {}}',
      );

      if (response.status.hasError) {
        _print(doPrint: debug, data: 'Public API POST Error: $url');
        return controller.loadFailed(requestCode: code, response: response);
      }

      return controller.loadSuccess(
        requestCode: code,
        response: response.body ?? {},
        statusCode: response.status.code ?? 0,
      );
    } catch (e) {
      Get.find<LoggerService>().log('Public API POST Error: $e');
      return controller.loadError(e);
    }
  }

  // Public API endpoints
  final String _publicRecruitment = '/public/agency/recruitment';
  final String _publicComboCategory = '/public/combo-category';
  final String _publicBranch = '/public/branch';
  final String _publicBank = '/public/bank';

  /// Submit recruitment form via public API
  Future<void> submitPublicRecruitmentForm({
    required BaseControllers controllers,
    required var data,
    int? code,
  }) async {
    await publicApiPost(
      url: _publicRecruitment,
      controller: controllers,
      data: data,
      debug: true,
      code: code ?? 0,
    );
  }

  /// Get combo categories via public API
  Future<void> getPublicComboCategories({
    required BaseControllers controllers,
    int? code,
  }) async {
    await publicApiFetch(
      url: _publicComboCategory,
      controller: controllers,
      debug: false,
      code: code ?? 0,
    );
  }

  /// Get branches via public API
  Future<void> getPublicBranches({
    required BaseControllers controllers,
    int? code,
  }) async {
    await publicApiFetch(
      url: _publicBranch,
      controller: controllers,
      debug: false,
      code: code ?? 0,
    );
  }

  /// Get banks via public API
  Future<void> getPublicBanks({
    required BaseControllers controllers,
    int? code,
  }) async {
    await publicApiFetch(
      url: _publicBank,
      controller: controllers,
      debug: false,
      code: code ?? 0,
    );
  }
}
